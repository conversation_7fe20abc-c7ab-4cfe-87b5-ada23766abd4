/**
 * 🔥 ПРОВЕРКА АДРЕСОВ ИЗ РЕАЛЬНОЙ ОШИБКИ 3012
 * ПРОВЕРЯЕМ ЧТО ВСЕ АДРЕСА ИЗ ОШИБКИ СУЩЕСТВУЮТ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function verifyErrorAddresses() {
    console.log('🔥 ПРОВЕРКА АДРЕСОВ ИЗ РЕАЛЬНОЙ ОШИБКИ 3012...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // АДРЕСА ИЗ РЕАЛЬНОЙ ОШИБКИ
        const errorAddresses = [
            // LB PAIR
            {
                name: '<PERSON><PERSON> (из ошибки)',
                address: 'BGm1tav5K4NjMXVVS6vGHPdJhZLhzNNdvCNvgqjYqiG8',
                type: 'LB Pair',
                critical: true
            },
            
            // RESERVES ИЗ ТРАНЗАКЦИИ #1
            {
                name: 'Reserve X (транзакция #1)',
                address: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF',
                type: 'Reserve',
                critical: true
            },
            {
                name: 'Reserve Y (транзакция #1)',
                address: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD',
                type: 'Reserve',
                critical: true
            },
            
            // RESERVES ИЗ ТРАНЗАКЦИИ #2
            {
                name: 'Reserve X (транзакция #2)',
                address: 'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq',
                type: 'Reserve',
                critical: true
            },
            {
                name: 'Reserve Y (транзакция #2)',
                address: 'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi',
                type: 'Reserve',
                critical: true
            },
            
            // BIN ARRAYS
            {
                name: 'Bin Array #1',
                address: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF',
                type: 'Bin Array',
                critical: true
            },
            {
                name: 'Bin Array #2',
                address: '7YLfregNFN6jihCbcBSCLdwVUKh7aM78znyUHfCj4kbf',
                type: 'Bin Array',
                critical: true
            },
            
            // EVENT AUTHORITY
            {
                name: 'Event Authority',
                address: 'C4HDwjH7JWkU1C8aKSHx9akFCMrRu9zLB9nuLARXpwRR',
                type: 'Event Authority',
                critical: false
            },
            
            // BITMAP EXTENSION
            {
                name: 'Bin Array Bitmap Extension',
                address: 'DArpuuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE',
                type: 'Bitmap Extension',
                critical: true
            },
            
            // POSITIONS
            {
                name: 'Position #1',
                address: 'AegDe4QfxF48RxTXowrcH9yfFR7873WkC9WDKwmJjX2a',
                type: 'Position',
                critical: true
            },
            {
                name: 'Position #2',
                address: 'BuKyq1rw3rFQX3JRbVqMcQAcyNs4arTBCWLvQGnu6nKJ',
                type: 'Position',
                critical: true
            }
        ];

        console.log(`📊 ПРОВЕРЯЕМ ${errorAddresses.length} АДРЕСОВ ИЗ ОШИБКИ...\n`);

        let totalChecked = 0;
        let totalExists = 0;
        let criticalMissing = 0;
        const results = [];

        // Проверяем каждый адрес
        for (const item of errorAddresses) {
            totalChecked++;
            console.log(`🔍 ${item.name}:`);
            console.log(`   Адрес: ${item.address}`);
            console.log(`   Тип: ${item.type}`);
            console.log(`   Критический: ${item.critical ? 'ДА' : 'НЕТ'}`);

            try {
                const publicKey = new PublicKey(item.address);
                const accountInfo = await connection.getAccountInfo(publicKey);

                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   💰 Lamports: ${accountInfo.lamports}`);
                    console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
                    totalExists++;
                    results.push({ ...item, exists: true, size: accountInfo.data.length });
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ!`);
                    if (item.critical) {
                        criticalMissing++;
                        console.log(`   🚨 КРИТИЧЕСКИЙ АККАУНТ ОТСУТСТВУЕТ!`);
                    }
                    results.push({ ...item, exists: false });
                }
            } catch (error) {
                console.log(`   💥 ОШИБКА: ${error.message}`);
                if (item.critical) {
                    criticalMissing++;
                }
                results.push({ ...item, exists: false, error: error.message });
            }

            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ИТОГОВЫЙ ОТЧЁТ
        console.log('🎯 ИТОГОВЫЙ ОТЧЁТ:');
        console.log(`   ✅ Существующие аккаунты: ${totalExists}/${totalChecked}`);
        console.log(`   🚨 Критические отсутствующие: ${criticalMissing}`);
        console.log(`   📊 Процент успеха: ${Math.round((totalExists / totalChecked) * 100)}%\n`);

        // АНАЛИЗ КРИТИЧЕСКИХ ПРОБЛЕМ
        const missingCritical = results.filter(r => !r.exists && r.critical);
        if (missingCritical.length > 0) {
            console.log('🚨 КРИТИЧЕСКИЕ ОТСУТСТВУЮЩИЕ АККАУНТЫ:');
            missingCritical.forEach((item, index) => {
                console.log(`   ${index + 1}. ${item.name} (${item.type})`);
                console.log(`      ${item.address}`);
            });
            console.log('\n❌ ЭТО ПРИЧИНА ОШИБКИ 3012!');
        } else {
            console.log('✅ ВСЕ КРИТИЧЕСКИЕ АККАУНТЫ СУЩЕСТВУЮТ!');
        }

        return {
            totalChecked,
            totalExists,
            criticalMissing,
            results,
            success: criticalMissing === 0
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск проверки
if (require.main === module) {
    verifyErrorAddresses()
        .then(result => {
            if (result && result.success) {
                console.log('\n✅ ВСЕ КРИТИЧЕСКИЕ АДРЕСА СУЩЕСТВУЮТ!');
                process.exit(0);
            } else if (result) {
                console.log(`\n❌ НАЙДЕНЫ КРИТИЧЕСКИЕ ПРОБЛЕМЫ! Отсутствует: ${result.criticalMissing}`);
                process.exit(1);
            } else {
                console.log('\n💥 ОШИБКА ПРОВЕРКИ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { verifyErrorAddresses };
