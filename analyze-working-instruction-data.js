/**
 * 🔍 АНАЛИЗ РАБОЧЕЙ INSTRUCTION DATA
 * Разбираем hex данные из рабочей транзакции
 */

function analyzeWorkingInstructionData() {
    console.log('🔍 АНАЛИЗ РАБОЧЕЙ INSTRUCTION DATA');
    console.log('='.repeat(60));

    // Рабочая instruction data из твоего примера
    const workingHex = '2e6a175c1a2b8d4f00ca9a3b0000000000ca9a3b00000000030000004deeffff050d050d4eeeffff060d060d4feeffff050d050d010000000000000001000000';
    const workingBuffer = Buffer.from(workingHex, 'hex');

    console.log(`📊 Общая длина: ${workingBuffer.length} bytes`);
    console.log(`📊 Hex: ${workingHex}`);

    let offset = 0;

    // 1. Discriminator (8 bytes)
    const discriminator = workingBuffer.slice(offset, offset + 8);
    console.log(`\n1️⃣ DISCRIMINATOR (8 bytes):`);
    console.log(`   Hex: ${discriminator.toString('hex')}`);
    console.log(`   Array: [${Array.from(discriminator).map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
    offset += 8;

    // 2. Amount X (8 bytes, u64 little-endian)
    const amountX = workingBuffer.readBigUInt64LE(offset);
    console.log(`\n2️⃣ AMOUNT X (8 bytes, u64 LE):`);
    console.log(`   Hex: ${workingBuffer.slice(offset, offset + 8).toString('hex')}`);
    console.log(`   Value: ${amountX.toString()}`);
    console.log(`   Decimal: ${Number(amountX) / 1e9} WSOL`);
    offset += 8;

    // 3. Amount Y (8 bytes, u64 little-endian)
    const amountY = workingBuffer.readBigUInt64LE(offset);
    console.log(`\n3️⃣ AMOUNT Y (8 bytes, u64 LE):`);
    console.log(`   Hex: ${workingBuffer.slice(offset, offset + 8).toString('hex')}`);
    console.log(`   Value: ${amountY.toString()}`);
    console.log(`   Decimal: ${Number(amountY) / 1e6} USDC`);
    offset += 8;

    // 4. Bin Liquidity Distribution Count (4 bytes, u32 little-endian)
    const binCount = workingBuffer.readUInt32LE(offset);
    console.log(`\n4️⃣ BIN COUNT (4 bytes, u32 LE):`);
    console.log(`   Hex: ${workingBuffer.slice(offset, offset + 4).toString('hex')}`);
    console.log(`   Value: ${binCount}`);
    offset += 4;

    // 5. Bin Liquidity Distribution Array
    console.log(`\n5️⃣ BIN LIQUIDITY DISTRIBUTION (${binCount} элементов):`);
    for (let i = 0; i < binCount; i++) {
        console.log(`\n   Bin ${i + 1}:`);
        
        // binId (4 bytes, i32 little-endian)
        const binId = workingBuffer.readInt32LE(offset);
        console.log(`     binId: ${binId} (hex: ${workingBuffer.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // xAmountBpsOfTotal (2 bytes, u16 little-endian)
        const xBps = workingBuffer.readUInt16LE(offset);
        console.log(`     xAmountBpsOfTotal: ${xBps} (hex: ${workingBuffer.slice(offset, offset + 2).toString('hex')})`);
        offset += 2;
        
        // yAmountBpsOfTotal (2 bytes, u16 little-endian)
        const yBps = workingBuffer.readUInt16LE(offset);
        console.log(`     yAmountBpsOfTotal: ${yBps} (hex: ${workingBuffer.slice(offset, offset + 2).toString('hex')})`);
        offset += 2;
    }

    // 6. Remaining Accounts Info
    console.log(`\n6️⃣ REMAINING ACCOUNTS INFO:`);
    
    if (offset < workingBuffer.length) {
        // slices count (4 bytes, u32 little-endian)
        const slicesCount = workingBuffer.readUInt32LE(offset);
        console.log(`   Slices count: ${slicesCount} (hex: ${workingBuffer.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // start index (4 bytes, u32 little-endian)
        const startIndex = workingBuffer.readUInt32LE(offset);
        console.log(`   Start index: ${startIndex} (hex: ${workingBuffer.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // length (4 bytes, u32 little-endian)
        const length = workingBuffer.readUInt32LE(offset);
        console.log(`   Length: ${length} (hex: ${workingBuffer.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
    }

    console.log(`\n📊 ИТОГО РАЗОБРАНО: ${offset} bytes из ${workingBuffer.length}`);
    
    if (offset < workingBuffer.length) {
        console.log(`⚠️ ОСТАЛИСЬ НЕРАЗОБРАННЫЕ ДАННЫЕ: ${workingBuffer.slice(offset).toString('hex')}`);
    }

    // Генерируем нашу версию для сравнения
    console.log(`\n🔄 ГЕНЕРИРУЕМ НАШУ ВЕРСИЮ ДЛЯ СРАВНЕНИЯ:`);
    
    const ourData = generateOurInstructionData({
        amountX: Number(amountX),
        amountY: Number(amountY),
        binLiquidityDist: [
            { binId: -4531, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 },
            { binId: -4530, xAmountBpsOfTotal: 3334, yAmountBpsOfTotal: 3334 },
            { binId: -4529, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 }
        ]
    });

    console.log(`   Наша hex: ${ourData.toString('hex')}`);
    console.log(`   Рабочая:  ${workingHex}`);
    console.log(`   Совпадает: ${ourData.toString('hex') === workingHex ? '✅' : '❌'}`);

    return {
        discriminator: Array.from(discriminator),
        amountX: Number(amountX),
        amountY: Number(amountY),
        binCount,
        workingHex,
        ourHex: ourData.toString('hex')
    };
}

function generateOurInstructionData(params) {
    const { amountX, amountY, binLiquidityDist } = params;
    
    const data = Buffer.alloc(256);
    let offset = 0;
    
    // Discriminator
    const discriminator = Buffer.from([0x2e, 0x6a, 0x17, 0x5c, 0x1a, 0x2b, 0x8d, 0x4f]);
    discriminator.copy(data, offset);
    offset += 8;
    
    // amount_x (u64)
    data.writeBigUInt64LE(BigInt(amountX), offset);
    offset += 8;
    
    // amount_y (u64)
    data.writeBigUInt64LE(BigInt(amountY), offset);
    offset += 8;
    
    // bin_liquidity_dist count (u32)
    data.writeUInt32LE(binLiquidityDist.length, offset);
    offset += 4;
    
    // bin_liquidity_dist array
    for (const dist of binLiquidityDist) {
        data.writeInt32LE(dist.binId, offset);
        offset += 4;
        data.writeBigUInt64LE(10000n, offset); // 🔥 ИСПРАВЛЕНО: distribution_x = 10000 (U64)
        offset += 8;
        data.writeBigUInt64LE(10000n, offset); // 🔥 ИСПРАВЛЕНО: distribution_y = 10000 (U64)
        offset += 8;
    }
    
    // remaining_accounts_info
    data.writeUInt32LE(1, offset); // slices count
    offset += 4;
    data.writeUInt32LE(0, offset); // start index
    offset += 4;
    data.writeUInt32LE(1, offset); // length
    offset += 4;
    
    return data.slice(0, offset);
}

if (require.main === module) {
    analyzeWorkingInstructionData();
}

module.exports = { analyzeWorkingInstructionData };
