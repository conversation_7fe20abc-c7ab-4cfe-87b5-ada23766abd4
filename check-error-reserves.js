/**
 * 🔥 ПРОВЕРКА RESERVE АДРЕСОВ ИЗ ОШИБКИ 3012
 * ПРОВЕРЯЕМ СУЩЕСТВУЮТ ЛИ RESERVE АДРЕСА ИЗ РЕАЛЬНОЙ ОШИБКИ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function checkErrorReserves() {
    console.log('🔥 ПРОВЕРКА RESERVE АДРЕСОВ ИЗ ОШИБКИ 3012...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // RESERVE АДРЕСА ИЗ ОШИБКИ
        const errorReserves = [
            {
                name: 'Transaction 1 - Reserve X',
                address: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF',
                transaction: 'add_liquidity2 #1'
            },
            {
                name: 'Transaction 1 - Reserve Y',
                address: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD',
                transaction: 'add_liquidity2 #1'
            },
            {
                name: 'Transaction 2 - Reserve X',
                address: 'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq',
                transaction: 'add_liquidity2 #2'
            },
            {
                name: 'Transaction 2 - Reserve Y',
                address: 'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi',
                transaction: 'add_liquidity2 #2'
            }
        ];

        // LB PAIR АДРЕСА ИЗ ОШИБКИ
        const errorLBPairs = [
            {
                name: 'LB Pair из ошибки',
                address: 'BGm1tav5K4NjMXVVS6vGHPdJhZLhzNNdvCNvgqjYqiG8', // Из ошибки
                note: 'Из транзакций add_liquidity2'
            }
        ];

        // BIN ARRAYS ИЗ ОШИБКИ
        const errorBinArrays = [
            {
                name: 'Bin Array #1',
                address: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF',
                transaction: 'add_liquidity2 #1'
            },
            {
                name: 'Bin Array #2',
                address: '7YLfregNFN6jihCbcBSCLdwVUKh7aM78znyUHfCj4kbf',
                transaction: 'add_liquidity2 #2'
            }
        ];

        // EVENT AUTHORITY ИЗ ОШИБКИ
        const errorEventAuthority = [
            {
                name: 'Event Authority',
                address: 'C4HDwjH7JWkU1C8aKSHx9akFCMrRu9zLB9nuLARXpwRR',
                note: 'Из всех транзакций'
            },
            {
                name: 'Event Authority (swap2)',
                address: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
                note: 'Из транзакции swap2'
            }
        ];

        console.log('📊 ПРОВЕРЯЕМ ВСЕ АДРЕСА ИЗ ОШИБКИ...\n');

        let totalChecked = 0;
        let totalExists = 0;

        // Функция проверки аккаунта
        async function checkAccount(item) {
            totalChecked++;
            console.log(`🔍 ${item.name}:`);
            console.log(`   Адрес: ${item.address}`);
            console.log(`   Источник: ${item.transaction || item.note}`);

            try {
                const publicKey = new PublicKey(item.address);
                const accountInfo = await connection.getAccountInfo(publicKey);

                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   💰 Lamports: ${accountInfo.lamports}`);
                    console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
                    totalExists++;
                    return { ...item, exists: true, size: accountInfo.data.length, owner: accountInfo.owner.toString() };
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ!`);
                    return { ...item, exists: false };
                }
            } catch (error) {
                console.log(`   💥 ОШИБКА: ${error.message}`);
                return { ...item, exists: false, error: error.message };
            }
        }

        // ПРОВЕРЯЕМ RESERVE АДРЕСА
        console.log('🔥 ПРОВЕРКА RESERVE АДРЕСОВ ИЗ ОШИБКИ:');
        const reserveResults = [];
        for (const reserve of errorReserves) {
            const result = await checkAccount(reserve);
            reserveResults.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ПРОВЕРЯЕМ LB PAIRS
        console.log('🔥 ПРОВЕРКА LB PAIRS ИЗ ОШИБКИ:');
        const lbPairResults = [];
        for (const lbPair of errorLBPairs) {
            const result = await checkAccount(lbPair);
            lbPairResults.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ПРОВЕРЯЕМ BIN ARRAYS
        console.log('🔥 ПРОВЕРКА BIN ARRAYS ИЗ ОШИБКИ:');
        const binArrayResults = [];
        for (const binArray of errorBinArrays) {
            const result = await checkAccount(binArray);
            binArrayResults.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ПРОВЕРЯЕМ EVENT AUTHORITY
        console.log('🔥 ПРОВЕРКА EVENT AUTHORITY ИЗ ОШИБКИ:');
        const eventAuthorityResults = [];
        for (const eventAuth of errorEventAuthority) {
            const result = await checkAccount(eventAuth);
            eventAuthorityResults.push(result);
            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ИТОГОВЫЙ ОТЧЁТ
        console.log('🎯 ИТОГОВЫЙ ОТЧЁТ:');
        console.log(`   ✅ Существующие аккаунты: ${totalExists}/${totalChecked}`);
        console.log(`   📊 Процент успеха: ${Math.round((totalExists / totalChecked) * 100)}%\n`);

        // АНАЛИЗ РЕЗУЛЬТАТОВ
        const existingReserves = reserveResults.filter(r => r.exists);
        const existingLBPairs = lbPairResults.filter(r => r.exists);
        const existingBinArrays = binArrayResults.filter(r => r.exists);
        const existingEventAuth = eventAuthorityResults.filter(r => r.exists);

        console.log('📋 СУЩЕСТВУЮЩИЕ АДРЕСА ИЗ ОШИБКИ:');
        
        if (existingReserves.length > 0) {
            console.log('\n✅ RESERVE АДРЕСА:');
            existingReserves.forEach(r => {
                console.log(`   ${r.name}: ${r.address}`);
            });
        }

        if (existingLBPairs.length > 0) {
            console.log('\n✅ LB PAIRS:');
            existingLBPairs.forEach(r => {
                console.log(`   ${r.name}: ${r.address}`);
            });
        }

        if (existingBinArrays.length > 0) {
            console.log('\n✅ BIN ARRAYS:');
            existingBinArrays.forEach(r => {
                console.log(`   ${r.name}: ${r.address}`);
            });
        }

        if (existingEventAuth.length > 0) {
            console.log('\n✅ EVENT AUTHORITY:');
            existingEventAuth.forEach(r => {
                console.log(`   ${r.name}: ${r.address}`);
            });
        }

        return {
            reserves: reserveResults,
            lbPairs: lbPairResults,
            binArrays: binArrayResults,
            eventAuthority: eventAuthorityResults,
            existingReserves,
            existingLBPairs,
            existingBinArrays,
            existingEventAuth
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск проверки
if (require.main === module) {
    checkErrorReserves()
        .then(result => {
            if (result) {
                console.log('\n🔥 ПРОВЕРКА ЗАВЕРШЕНА!');
                process.exit(0);
            } else {
                console.log('\n💥 ОШИБКА ПРОВЕРКИ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { checkErrorReserves };
