/**
 * 🔧 СОЗДАНИЕ RESERVE PDA ДЛЯ METEORA DLMM
 */

const { Connection, PublicKey, SystemProgram, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const fs = require('fs');

// Константы
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const LB_PAIR_POOL_1 = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
const LB_PAIR_POOL_2 = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

// RPC подключение
const connection = new Connection('https://solana-mainnet.api.syndica.io/api-key/2k9VGQQAaaduhNKRVzLABzjzbFVcK9WpMynpaVEkLgcUdfdEaVsEUA3CQ1WiFVThGEBCaGtz2269oPXPDwaRkNdgeL3inNgtpvU');

// Кошелек (загружаем из файла)
function loadWallet() {
    try {
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        return require('@solana/web3.js').Keypair.fromSecretKey(new Uint8Array(secretKey));
    } catch (error) {
        throw new Error('Не удалось загрузить кошелек из wallet.json');
    }
}

/**
 * 🔧 Генерация Reserve PDA
 */
function generateReservePDA(lbPair, tokenMint) {
    const seeds = [
        Buffer.from('reserve'),
        lbPair.toBuffer(),
        tokenMint.toBuffer()
    ];
    
    return PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM_ID);
}

/**
 * 🔍 Проверка существования Reserve PDA
 */
async function checkReservePDAExists(reservePDA) {
    try {
        const accountInfo = await connection.getAccountInfo(reservePDA);
        return accountInfo !== null;
    } catch (error) {
        console.error(`Ошибка проверки PDA ${reservePDA.toBase58()}:`, error.message);
        return false;
    }
}

/**
 * 🔧 Создание инструкции для Reserve PDA
 */
async function createReservePDAInstruction(lbPair, tokenMint, payer) {
    const [reservePDA] = generateReservePDA(lbPair, tokenMint);
    
    // Проверяем существует ли уже
    const exists = await checkReservePDAExists(reservePDA);
    if (exists) {
        console.log(`   ✅ Reserve PDA уже существует: ${reservePDA.toBase58()}`);
        return null;
    }
    
    console.log(`   🔧 Создаем Reserve PDA: ${reservePDA.toBase58()}`);
    
    // Получаем минимальную арендную плату
    const rent = await connection.getMinimumBalanceForRentExemption(165); // Размер Reserve аккаунта
    
    // Создаем инструкцию
    const createAccountIx = SystemProgram.createAccount({
        fromPubkey: payer,
        newAccountPubkey: reservePDA,
        lamports: rent,
        space: 165, // Размер Reserve аккаунта в Meteora
        programId: METEORA_PROGRAM_ID
    });
    
    return createAccountIx;
}

/**
 * 🚀 Создание всех Reserve PDA
 */
async function createAllReservePDAs() {
    console.log('🔧 СОЗДАНИЕ ВСЕХ RESERVE PDA ДЛЯ METEORA DLMM');
    console.log('='.repeat(50));
    
    const wallet = loadWallet();
    console.log(`👤 Кошелек: ${wallet.publicKey.toBase58()}`);
    
    const instructions = [];
    
    // Reserve PDA для Pool 1
    console.log('\n📊 POOL 1 RESERVE PDA:');
    const pool1ReserveXIx = await createReservePDAInstruction(LB_PAIR_POOL_1, WSOL_MINT, wallet.publicKey);
    const pool1ReserveYIx = await createReservePDAInstruction(LB_PAIR_POOL_1, USDC_MINT, wallet.publicKey);
    
    if (pool1ReserveXIx) instructions.push(pool1ReserveXIx);
    if (pool1ReserveYIx) instructions.push(pool1ReserveYIx);
    
    // Reserve PDA для Pool 2
    console.log('\n📊 POOL 2 RESERVE PDA:');
    const pool2ReserveXIx = await createReservePDAInstruction(LB_PAIR_POOL_2, WSOL_MINT, wallet.publicKey);
    const pool2ReserveYIx = await createReservePDAInstruction(LB_PAIR_POOL_2, USDC_MINT, wallet.publicKey);
    
    if (pool2ReserveXIx) instructions.push(pool2ReserveXIx);
    if (pool2ReserveYIx) instructions.push(pool2ReserveYIx);
    
    if (instructions.length === 0) {
        console.log('\n✅ ВСЕ RESERVE PDA УЖЕ СУЩЕСТВУЮТ!');
        return;
    }
    
    console.log(`\n🔧 СОЗДАНИЕ ТРАНЗАКЦИИ С ${instructions.length} ИНСТРУКЦИЯМИ...`);
    
    // Создаем транзакцию
    const transaction = new Transaction();
    instructions.forEach(ix => transaction.add(ix));
    
    // Получаем последний blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = wallet.publicKey;
    
    try {
        console.log('📤 ОТПРАВКА ТРАНЗАКЦИИ...');
        const signature = await sendAndConfirmTransaction(connection, transaction, [wallet], {
            commitment: 'confirmed',
            maxRetries: 3
        });
        
        console.log(`\n🎉 ТРАНЗАКЦИЯ УСПЕШНА!`);
        console.log(`   Signature: ${signature}`);
        console.log(`   Explorer: https://solscan.io/tx/${signature}`);
        
        // Проверяем созданные PDA
        console.log('\n🔍 ПРОВЕРКА СОЗДАННЫХ RESERVE PDA:');
        
        const reserves = [
            { name: 'Pool 1 Reserve X', lbPair: LB_PAIR_POOL_1, tokenMint: WSOL_MINT },
            { name: 'Pool 1 Reserve Y', lbPair: LB_PAIR_POOL_1, tokenMint: USDC_MINT },
            { name: 'Pool 2 Reserve X', lbPair: LB_PAIR_POOL_2, tokenMint: WSOL_MINT },
            { name: 'Pool 2 Reserve Y', lbPair: LB_PAIR_POOL_2, tokenMint: USDC_MINT }
        ];
        
        for (const reserve of reserves) {
            const [reservePDA] = generateReservePDA(reserve.lbPair, reserve.tokenMint);
            const exists = await checkReservePDAExists(reservePDA);
            console.log(`   ${exists ? '✅' : '❌'} ${reserve.name}: ${reservePDA.toBase58()}`);
        }
        
    } catch (error) {
        console.error('\n💥 ОШИБКА ОТПРАВКИ ТРАНЗАКЦИИ:', error.message);
        
        if (error.logs) {
            console.log('\n📋 ЛОГИ ТРАНЗАКЦИИ:');
            error.logs.forEach(log => console.log(`   ${log}`));
        }
    }
}

/**
 * 🔍 Проверка текущего состояния Reserve PDA
 */
async function checkCurrentReservePDAs() {
    console.log('🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ RESERVE PDA');
    console.log('='.repeat(50));
    
    const reserves = [
        { name: 'Pool 1 Reserve X', lbPair: LB_PAIR_POOL_1, tokenMint: WSOL_MINT },
        { name: 'Pool 1 Reserve Y', lbPair: LB_PAIR_POOL_1, tokenMint: USDC_MINT },
        { name: 'Pool 2 Reserve X', lbPair: LB_PAIR_POOL_2, tokenMint: WSOL_MINT },
        { name: 'Pool 2 Reserve Y', lbPair: LB_PAIR_POOL_2, tokenMint: USDC_MINT }
    ];
    
    for (const reserve of reserves) {
        const [reservePDA] = generateReservePDA(reserve.lbPair, reserve.tokenMint);
        const exists = await checkReservePDAExists(reservePDA);
        
        console.log(`\n🔍 ${reserve.name}:`);
        console.log(`   Адрес: ${reservePDA.toBase58()}`);
        console.log(`   Существует: ${exists ? '✅ ДА' : '❌ НЕТ'}`);
        
        if (exists) {
            try {
                const accountInfo = await connection.getAccountInfo(reservePDA);
                console.log(`   Lamports: ${accountInfo.lamports.toLocaleString()}`);
                console.log(`   Data Length: ${accountInfo.data.length} bytes`);
                console.log(`   Owner: ${accountInfo.owner.toBase58()}`);
            } catch (error) {
                console.log(`   Ошибка получения данных: ${error.message}`);
            }
        }
    }
}

// Запуск
if (require.main === module) {
    const command = process.argv[2];
    
    if (command === 'check') {
        checkCurrentReservePDAs().catch(console.error);
    } else {
        createAllReservePDAs().catch(console.error);
    }
}

module.exports = { createAllReservePDAs, checkCurrentReservePDAs, generateReservePDA };
