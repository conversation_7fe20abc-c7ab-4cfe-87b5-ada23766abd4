const { Connection, PublicKey } = require('@solana/web3.js');

async function findRealBitmapExtensions() {
    console.log('🔍 ИЩЕМ РЕАЛЬНЫЕ BITMAP EXTENSION PDA В БЛОКЧЕЙНЕ...');
    
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    const METEORA_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    
    const pools = [
        { name: 'POOL_1', address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', activeBin: -4560 },
        { name: 'POOL_2', address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', activeBin: -1825 }
    ];
    
    for (const pool of pools) {
        console.log('');
        console.log('📊', pool.name, '- ПОИСК BITMAP EXTENSION:');
        console.log('   Pool:', pool.address);
        console.log('   Активный bin:', pool.activeBin);
        
        const poolPubkey = new PublicKey(pool.address);
        const targetIndex = Math.floor(pool.activeBin / 64);
        console.log('   Целевой index:', targetIndex);
        
        let found = false;
        
        // Проверяем разные варианты seeds для Meteora DLMM
        const seedVariants = [
            'bin_array_bitmap_extension',
            'bitmap_extension', 
            'bin_bitmap_extension',
            'array_bitmap_extension',
            'bitmap',
            'extension',
            'bin_array_bitmap',
            'dlmm_bitmap_extension',
            'meteora_bitmap_extension',
            'lb_bitmap_extension',
            'liquidity_bitmap_extension',
            'bin_extension',
            'array_extension'
        ];
        
        for (const seedString of seedVariants) {
            console.log('   Проверяем seed:', seedString);
            
            // Проверяем диапазон индексов вокруг целевого
            for (let indexOffset = -2; indexOffset <= 2; indexOffset++) {
                const testIndex = targetIndex + indexOffset;
                
                try {
                    // Вариант 1: int32LE
                    const indexBuffer = Buffer.alloc(4);
                    indexBuffer.writeInt32LE(testIndex, 0);
                    
                    const seeds1 = [Buffer.from(seedString), poolPubkey.toBuffer(), indexBuffer];
                    const [pda1] = PublicKey.findProgramAddressSync(seeds1, METEORA_PROGRAM);
                    
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    const account1 = await connection.getAccountInfo(pda1);
                    if (account1 && account1.owner.equals(METEORA_PROGRAM)) {
                        console.log('      НАЙДЕН! Seed:', seedString, 'Index:', testIndex, 'Вариант: int32LE');
                        console.log('      PDA:', pda1.toString());
                        console.log('      Owner:', account1.owner.toString());
                        console.log('      Data length:', account1.data.length, 'bytes');
                        
                        // Показываем первые байты данных для анализа
                        if (account1.data.length > 0) {
                            const firstBytes = account1.data.slice(0, Math.min(32, account1.data.length));
                            console.log('      Первые байты:', firstBytes.toString('hex'));
                        }
                        
                        found = true;
                        break;
                    }
                    
                    // Вариант 2: int32BE
                    const indexBufferBE = Buffer.alloc(4);
                    indexBufferBE.writeInt32BE(testIndex, 0);
                    
                    const seeds2 = [Buffer.from(seedString), poolPubkey.toBuffer(), indexBufferBE];
                    const [pda2] = PublicKey.findProgramAddressSync(seeds2, METEORA_PROGRAM);
                    
                    const account2 = await connection.getAccountInfo(pda2);
                    if (account2 && account2.owner.equals(METEORA_PROGRAM)) {
                        console.log('      НАЙДЕН! Seed:', seedString, 'Index:', testIndex, 'Вариант: int32BE');
                        console.log('      PDA:', pda2.toString());
                        console.log('      Owner:', account2.owner.toString());
                        console.log('      Data length:', account2.data.length, 'bytes');
                        
                        // Показываем первые байты данных для анализа
                        if (account2.data.length > 0) {
                            const firstBytes = account2.data.slice(0, Math.min(32, account2.data.length));
                            console.log('      Первые байты:', firstBytes.toString('hex'));
                        }
                        
                        found = true;
                        break;
                    }
                    
                    // Вариант 3: без индекса (только pool)
                    const seeds3 = [Buffer.from(seedString), poolPubkey.toBuffer()];
                    const [pda3] = PublicKey.findProgramAddressSync(seeds3, METEORA_PROGRAM);
                    
                    const account3 = await connection.getAccountInfo(pda3);
                    if (account3 && account3.owner.equals(METEORA_PROGRAM)) {
                        console.log('      НАЙДЕН! Seed:', seedString, 'Без индекса');
                        console.log('      PDA:', pda3.toString());
                        console.log('      Owner:', account3.owner.toString());
                        console.log('      Data length:', account3.data.length, 'bytes');
                        
                        // Показываем первые байты данных для анализа
                        if (account3.data.length > 0) {
                            const firstBytes = account3.data.slice(0, Math.min(32, account3.data.length));
                            console.log('      Первые байты:', firstBytes.toString('hex'));
                        }
                        
                        found = true;
                        break;
                    }
                    
                } catch (error) {
                    if (error.message.includes('429')) {
                        console.log('Server responded with 429 Too Many Requests.  Retrying after 500ms delay...');
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
            }
            
            if (found) break;
        }
        
        if (!found) {
            console.log('   НЕ НАЙДЕН bitmap extension для', pool.name);
            console.log('   Попробуем поиск всех PDA для этого пула...');
            
            // Дополнительный поиск - ищем все PDA связанные с пулом
            try {
                console.log('   Поиск всех PDA связанных с пулом...');
                const poolPubkey = new PublicKey(pool.address);
                
                // Получаем все аккаунты принадлежащие Meteora DLMM программе
                const accounts = await connection.getProgramAccounts(METEORA_PROGRAM, {
                    filters: [
                        {
                            memcmp: {
                                offset: 0,
                                bytes: poolPubkey.toBase58()
                            }
                        }
                    ]
                });
                
                console.log('   Найдено', accounts.length, 'аккаунтов связанных с пулом');
                
                for (let i = 0; i < Math.min(5, accounts.length); i++) {
                    const account = accounts[i];
                    console.log('   Аккаунт', i + 1, ':', account.pubkey.toString());
                    console.log('      Data length:', account.account.data.length, 'bytes');
                    if (account.account.data.length > 0) {
                        const firstBytes = account.account.data.slice(0, Math.min(16, account.account.data.length));
                        console.log('      Первые байты:', firstBytes.toString('hex'));
                    }
                }
                
            } catch (error) {
                console.log('   Ошибка при поиске связанных аккаунтов:', error.message);
            }
        }
    }
    
    console.log('');
    console.log(' ПОИСК ЗАВЕРШЕН!');
    console.log(' Если bitmap extensions не найдены, возможно:');
    console.log('   - Используется другая структура seeds');
    console.log('   - Bitmap extensions создаются динамически при необходимости');
    console.log('   - Требуется анализ исходного кода Meteora DLMM программы');
}

findRealBitmapExtensions().catch(console.error);
