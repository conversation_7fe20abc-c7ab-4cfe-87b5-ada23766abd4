/**
 * 🔥 СОЗДАНИЕ RESERVE PDA ЧЕРЕЗ МИНИМАЛЬНЫЕ SWAP ТРАНЗАКЦИИ
 * ОДИН РАЗ НА КАЖДЫЙ ПУЛ - СОЗДАЕТ RESERVE PDA НАВСЕГДА!
 */

const { Connection, PublicKey, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction } = require('@solana/spl-token');
const DLMM = require('@meteora-ag/dlmm').default;
const { AnchorProvider, Wallet } = require('@coral-xyz/anchor'); // ДЛЯ ANCHOR PROVIDER!
const fs = require('fs');
const BN = require('bn.js'); // Стандартный BN для Meteora SDK

// Константы
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

// Пулы для создания Reserve PDA
const POOLS = [
    {
        name: 'POOL_1',
        address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
        tokenX: WSOL_MINT,
        tokenY: USDC_MINT
    },
    {
        name: 'POOL_2', 
        address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
        tokenX: WSOL_MINT,
        tokenY: USDC_MINT
    }
];

// RPC подключение
require('dotenv').config({ path: '.env.solana' });
const connection = new Connection(process.env.HELIUS_RPC_URL || 'https://api.mainnet-beta.solana.com');

// Кошелек
function loadWallet() {
    try {
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        return require('@solana/web3.js').Keypair.fromSecretKey(new Uint8Array(secretKey));
    } catch (error) {
        throw new Error('Не удалось загрузить кошелек из wallet.json');
    }
}

/**
 * 🔧 Генерация Reserve PDA
 */
function generateReservePDA(lbPair, tokenMint) {
    const seeds = [
        Buffer.from('reserve'),
        lbPair.toBuffer(),
        tokenMint.toBuffer()
    ];
    
    return PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM_ID);
}

/**
 * 🔍 Проверка существования Reserve PDA
 */
async function checkReservePDAExists(reservePDA) {
    try {
        const accountInfo = await connection.getAccountInfo(reservePDA);
        return accountInfo !== null;
    } catch (error) {
        console.error(`Ошибка проверки PDA ${reservePDA.toBase58()}:`, error.message);
        return false;
    }
}

/**
 * 🔧 Создание минимального swap для инициализации Reserve PDA (РАБОЧИЙ КОД ИЗ complete-flash-loan-structure.js)
 */
async function createMinimalSwap(poolData, wallet) {
    console.log(`\n🔧 СОЗДАНИЕ МИНИМАЛЬНОГО SWAP ДЛЯ ${poolData.name}:`);
    console.log(`   Пул: ${poolData.address}`);

    try {
        // Проверяем существуют ли уже Reserve PDA
        const [reserveX] = generateReservePDA(new PublicKey(poolData.address), poolData.tokenX);
        const [reserveY] = generateReservePDA(new PublicKey(poolData.address), poolData.tokenY);

        const reserveXExists = await checkReservePDAExists(reserveX);
        const reserveYExists = await checkReservePDAExists(reserveY);

        console.log(`   Reserve X (${reserveX.toBase58()}): ${reserveXExists ? '✅ существует' : '❌ не существует'}`);
        console.log(`   Reserve Y (${reserveY.toBase58()}): ${reserveYExists ? '✅ существует' : '❌ не существует'}`);

        // 🔥 ПРИНУДИТЕЛЬНО СОЗДАЕМ SWAP ДАЖЕ ЕСЛИ КАЖЕТСЯ ЧТО PDA СУЩЕСТВУЮТ!
        console.log(`   🔧 ПРИНУДИТЕЛЬНО СОЗДАЕМ SWAP ДЛЯ ${poolData.name}...`);

        // 🔥 СОЗДАЕМ ANCHOR PROVIDER ДЛЯ SDK!
        console.log(`   🔧 Создание Anchor Provider для SDK...`);
        const anchorWallet = new Wallet(wallet);
        const provider = new AnchorProvider(connection, anchorWallet, {
            commitment: 'confirmed'
        });

        console.log(`   🔧 Создание DLMM объекта с правильным Provider...`);
        const dlmm = await DLMM.create(connection, new PublicKey(poolData.address), {
            cluster: 'mainnet-beta'
        });
        await dlmm.refetchStates();

        // 🔍 ОТЛАДКА - ПРОВЕРИМ ЧТО ПОЛУЧИЛ SDK
        console.log(`   🔍 DLMM объект создан:`);
        console.log(`   📊 LB Pair: ${dlmm.lbPair ? 'OK' : 'undefined'}`);
        console.log(`   💰 Token X: ${dlmm.tokenX ? 'OK' : 'undefined'}`);
        console.log(`   💰 Token Y: ${dlmm.tokenY ? 'OK' : 'undefined'}`);
        console.log(`   📈 Active Bin: ${dlmm.lbPair ? dlmm.lbPair.activeId : 'undefined'}`);

        // 🔥 ЕСЛИ SDK НЕ ЗАГРУЗИЛ ДАННЫЕ - ОШИБКА!
        if (!dlmm.tokenX || !dlmm.tokenY) {
            throw new Error('SDK не смог загрузить данные о токенах из пула!');
        }

        // Получаем token accounts
        const userTokenX = await getAssociatedTokenAddress(poolData.tokenX, wallet.publicKey);
        const userTokenY = await getAssociatedTokenAddress(poolData.tokenY, wallet.publicKey);

        console.log(`   💰 User Token X: ${userTokenX.toBase58()}`);
        console.log(`   💰 User Token Y: ${userTokenY.toBase58()}`);

        // 🔥 МИНИМАЛЬНАЯ СУММА ДЛЯ СОЗДАНИЯ RESERVE PDA
        const minSwapAmount = new BN(1000000); // 0.001 SOL (1M lamports)

        console.log(`   🔄 Создание swap инструкции через рабочий код...`);
        console.log(`   💰 Сумма: ${minSwapAmount.toString()} lamports (0.001 SOL)`);

        // 🔥 КОПИРУЕМ ТОЧНЫЙ РАБОЧИЙ КОД ИЗ complete-flash-loan-structure.js!
        console.log(`   🔧 Создание swap через РАБОЧИЙ КОД ИЗ ОСНОВНОГО ФАЙЛА...`);

        // Получаем информацию о пуле
        await dlmm.refetchStates();

        // 🔥 ПОЛУЧАЕМ АКТИВНЫЙ BIN ID
        const activeBinId = dlmm.lbPair.activeId;
        console.log(`   📊 Активный bin ID: ${activeBinId}`);

        // 🔥 ПОЛУЧАЕМ BIN ARRAYS ЧЕРЕЗ SDK КАК В ОСНОВНОМ КОДЕ!
        console.log(`   🔧 Получаем bin arrays через SDK...`);
        // 🔥 СОЗДАЕМ BIN ARRAYS КАК В ОСНОВНОМ КОДЕ!
        const CHUNK_SIZE = 70; // Константа из основного кода
        const ourBinIds = [activeBinId - 1, activeBinId, activeBinId + 1];
        const binArrayIndexes = ourBinIds.map(binId => Math.floor(binId / CHUNK_SIZE));
        const uniqueIndexes = [...new Set(binArrayIndexes)]; // Убираем дубли

        console.log(`   🎯 НАШИ БИНЫ: ${ourBinIds.join(', ')}`);
        console.log(`   🎯 CHUNKS: ${uniqueIndexes.join(', ')}`);

        const binArraysPubkeys = [];
        for (const index of uniqueIndexes) {
            const [binArrayPda] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    dlmm.pubkey.toBuffer(),
                    Buffer.from(index.toString().padStart(8, '0'), 'utf8')
                ],
                METEORA_PROGRAM_ID
            );
            binArraysPubkeys.push(binArrayPda);
        }

        // 🔥 ОГРАНИЧИВАЕМ ДО МАКСИМУМ 3 BIN ARRAYS (НАШ СТАНДАРТ!)
        const limitedBinArrays = binArraysPubkeys.slice(0, 3);

        console.log(`   ✅ СОЗДАНО ${limitedBinArrays.length} BIN ARRAYS:`);
        limitedBinArrays.forEach((pda, i) => {
            console.log(`     ${i + 1}. ${pda.toString().slice(0,8)}...`);
        });

        // 🔥 СОЗДАЕМ РУЧНОЙ swapQuote КАК В ОСНОВНОМ КОДЕ!
        const expectedOutAmount = minSwapAmount.div(new BN(200)); // Примерно 1/200 от входа
        const minOutAmount = expectedOutAmount.div(new BN(2)); // 50% slippage

        const swapQuote = {
            inAmount: minSwapAmount,
            outAmount: expectedOutAmount,
            minOutAmount: minOutAmount,
            binArraysPubkey: limitedBinArrays, // 🔥 ПРАВИЛЬНЫЕ BIN ARRAYS!
            fee: new BN(0)
        };

        console.log(`   📊 Swap Quote создан:`);
        console.log(`   💰 In Amount: ${minSwapAmount.toString()}`);
        console.log(`   💰 Expected Out: ${expectedOutAmount.toString()}`);
        console.log(`   💰 Min Out: ${minOutAmount.toString()}`);

        // 🔥 ТОЧНЫЙ КОД ИЗ ОСНОВНОГО ФАЙЛА!
        const inToken = dlmm.tokenX.publicKey; // WSOL
        const outToken = dlmm.tokenY.publicKey; // USDC

        const swapTx = await dlmm.swap({
            inToken: inToken,
            binArraysPubkey: swapQuote.binArraysPubkey, // Пустой массив
            inAmount: minSwapAmount,
            lbPair: dlmm.pubkey,
            user: wallet.publicKey,
            minOutAmount: swapQuote.minOutAmount,
            outToken: outToken,
            exactIn: true,
            userTokenIn: userTokenX,
            userTokenOut: userTokenY
        });

        console.log(`   ✅ Swap транзакция создана: ${swapTx.instructions.length} инструкций`);

        return swapTx;

    } catch (error) {
        console.error(`   ❌ Ошибка создания swap для ${poolData.name}: ${error.message}`);
        console.error(`   📋 Stack: ${error.stack}`);
        return null;
    }
}

/**
 * 🚀 Создание Reserve PDA для всех пулов
 */
async function createAllReservePDAs() {
    console.log('🔥 СОЗДАНИЕ RESERVE PDA ЧЕРЕЗ МИНИМАЛЬНЫЕ SWAP');
    console.log('='.repeat(50));
    
    const wallet = loadWallet();
    console.log(`👤 Кошелек: ${wallet.publicKey.toBase58()}`);
    
    // Проверяем баланс SOL
    const balance = await connection.getBalance(wallet.publicKey);
    console.log(`💰 Баланс SOL: ${(balance / 1e9).toFixed(6)} SOL`);
    
    if (balance < 10000000) { // 0.01 SOL
        throw new Error('❌ Недостаточно SOL для создания swap транзакций');
    }
    
    const results = [];
    
    // Создаем минимальные swap для каждого пула
    for (const poolData of POOLS) {
        const swapTx = await createMinimalSwap(poolData, wallet);
        
        if (swapTx) {
            results.push({
                pool: poolData.name,
                transaction: swapTx,
                poolAddress: poolData.address
            });
        }
    }
    
    if (results.length === 0) {
        console.log('\n✅ ВСЕ RESERVE PDA УЖЕ СУЩЕСТВУЮТ!');
        return;
    }
    
    console.log(`\n🔧 ОТПРАВКА ${results.length} SWAP ТРАНЗАКЦИЙ...`);
    
    // Отправляем каждую транзакцию отдельно
    for (const result of results) {
        try {
            console.log(`\n📤 ОТПРАВКА SWAP ДЛЯ ${result.pool}...`);
            
            // Получаем recent blockhash
            const { blockhash } = await connection.getLatestBlockhash();
            result.transaction.recentBlockhash = blockhash;
            result.transaction.feePayer = wallet.publicKey;
            
            // Подписываем и отправляем
            result.transaction.sign(wallet);
            
            const signature = await sendAndConfirmTransaction(
                connection, 
                result.transaction, 
                [wallet],
                {
                    commitment: 'confirmed',
                    maxRetries: 3
                }
            );
            
            console.log(`   ✅ SWAP УСПЕШЕН!`);
            console.log(`   Signature: ${signature}`);
            console.log(`   Explorer: https://solscan.io/tx/${signature}`);
            
            // Проверяем что Reserve PDA созданы
            await new Promise(resolve => setTimeout(resolve, 2000)); // Ждем 2 секунды
            
            const [reserveX] = generateReservePDA(new PublicKey(result.poolAddress), WSOL_MINT);
            const [reserveY] = generateReservePDA(new PublicKey(result.poolAddress), USDC_MINT);
            
            const reserveXExists = await checkReservePDAExists(reserveX);
            const reserveYExists = await checkReservePDAExists(reserveY);
            
            console.log(`   🔍 Reserve X: ${reserveXExists ? '✅ создан' : '❌ не создан'}`);
            console.log(`   🔍 Reserve Y: ${reserveYExists ? '✅ создан' : '❌ не создан'}`);
            
        } catch (error) {
            console.error(`   ❌ ОШИБКА SWAP ДЛЯ ${result.pool}: ${error.message}`);
            
            if (error.logs) {
                console.log('   📋 ЛОГИ:');
                error.logs.forEach(log => console.log(`     ${log}`));
            }
        }
    }
    
    console.log('\n🎉 СОЗДАНИЕ RESERVE PDA ЗАВЕРШЕНО!');
}

/**
 * 🔍 Проверка текущего состояния Reserve PDA
 */
async function checkAllReservePDAs() {
    console.log('🔍 ПРОВЕРКА ВСЕХ RESERVE PDA');
    console.log('='.repeat(50));
    
    for (const poolData of POOLS) {
        console.log(`\n📊 ${poolData.name} (${poolData.address}):`);
        
        const [reserveX] = generateReservePDA(new PublicKey(poolData.address), poolData.tokenX);
        const [reserveY] = generateReservePDA(new PublicKey(poolData.address), poolData.tokenY);
        
        const reserveXExists = await checkReservePDAExists(reserveX);
        const reserveYExists = await checkReservePDAExists(reserveY);
        
        console.log(`   Reserve X: ${reserveX.toBase58()} - ${reserveXExists ? '✅ существует' : '❌ не существует'}`);
        console.log(`   Reserve Y: ${reserveY.toBase58()} - ${reserveYExists ? '✅ существует' : '❌ не существует'}`);
    }
}

// Запуск
if (require.main === module) {
    const command = process.argv[2];
    
    if (command === 'check') {
        checkAllReservePDAs().catch(console.error);
    } else {
        createAllReservePDAs().catch(console.error);
    }
}

module.exports = { createAllReservePDAs, checkAllReservePDAs, generateReservePDA };
