/**
 * 🔥 АНАЛИЗ FAILED ТРАНЗАКЦИИ 4pZjtALkFGdPJfGf9ax7FJ3BceqS54YcmPeakbWi5vuPqBXNouG9nrqUw4RqTDpEEuFYcBet6XJ1oLSzwoeyr95q
 * ОШИБКА 3005 В INSTRUCTION #6
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MeteoraPDAGenerator } = require('./meteora-production-ready.js');
const rpcConfig = require('./rpc-config.js');

async function analyzeFailedTransaction() {
    console.log('🔥 АНАЛИЗ FAILED ТРАНЗАКЦИИ...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');

        // ДАННЫЕ ИЗ FAILED ТРАНЗАКЦИИ
        const failedSignature = '4pZjtALkFGdPJfGf9ax7FJ3BceqS54YcmPeakbWi5vuPqBXNouG9nrqUw4RqTDpEEuFYcBet6XJ1oLSzwoeyr95q';
        const lbPair = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6';
        
        // BIN ARRAY ИЗ FAILED ТРАНЗАКЦИИ
        const failedBinArray = '6p8p1B6JrEWQDC768pnQCj4u5s95oySDwmZPsVtoP5iN';
        
        console.log('📊 АНАЛИЗ FAILED ТРАНЗАКЦИИ:');
        console.log(`   Signature: ${failedSignature}`);
        console.log(`   LB Pair: ${lbPair}`);
        console.log(`   Failed Bin Array: ${failedBinArray}`);
        console.log('');

        // ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ FAILED BIN ARRAY
        console.log('🔍 ПРОВЕРКА FAILED BIN ARRAY:');
        const failedBinArrayInfo = await connection.getAccountInfo(new PublicKey(failedBinArray));
        console.log(`   Существует: ${failedBinArrayInfo ? '✅ ДА' : '❌ НЕТ'}`);
        
        if (failedBinArrayInfo) {
            console.log(`   Размер: ${failedBinArrayInfo.data.length} байт`);
            console.log(`   Lamports: ${failedBinArrayInfo.lamports}`);
            
            // Пытаемся определить bin array index
            try {
                const data = failedBinArrayInfo.data;
                let offset = 8; // skip discriminator
                const version = data.readUInt8(offset);
                offset += 1;
                const index = data.readBigInt64LE(offset);
                console.log(`   Version: ${version}`);
                console.log(`   Index: ${index}`);
            } catch (e) {
                console.log(`   Не удалось прочитать структуру: ${e.message}`);
            }
        }

        // СРАВНИВАЕМ С НАШИМИ BIN ARRAYS
        console.log('\n🔍 СРАВНЕНИЕ С НАШИМИ BIN ARRAYS:');
        
        // Наши тестовые бины
        const testBinIds = [-4053, -4052, -4051];
        console.log(`   Наши bin IDs: ${testBinIds.join(', ')}`);
        
        // Вычисляем наши bin array indices
        const ourBinArrayIndices = new Set();
        for (const binId of testBinIds) {
            const binArrayIndex = Math.floor(binId / 64);
            ourBinArrayIndices.add(binArrayIndex);
            console.log(`   Bin ID ${binId} -> Bin Array Index ${binArrayIndex}`);
        }
        
        console.log(`   Наши уникальные bin array indices: ${Array.from(ourBinArrayIndices).join(', ')}`);
        
        // Создаем PDA для наших bin arrays
        console.log('\n🔍 НАШИ BIN ARRAYS:');
        for (const index of ourBinArrayIndices) {
            const [binArrayPDA] = MeteoraPDAGenerator.getBinArrayPDA(lbPair, index);
            const accountInfo = await connection.getAccountInfo(binArrayPDA);
            
            console.log(`   Bin Array [${index}]: ${binArrayPDA.toString()}`);
            console.log(`   Существует: ${accountInfo ? '✅ ДА' : '❌ НЕТ'}`);
            console.log(`   Совпадает с failed: ${binArrayPDA.toString() === failedBinArray ? '✅ ДА' : '❌ НЕТ'}`);
            
            if (accountInfo) {
                console.log(`   Размер: ${accountInfo.data.length} байт`);
            }
        }

        // ПРОВЕРЯЕМ УСПЕШНЫЕ ТРАНЗАКЦИИ
        console.log('\n🔍 АНАЛИЗ УСПЕШНЫХ ТРАНЗАКЦИЙ:');
        
        // Успешная транзакция #6
        const successTx6BinArrays = [
            '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // #15
            '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4'  // #16
        ];
        
        console.log('   Успешная транзакция #6:');
        for (let i = 0; i < successTx6BinArrays.length; i++) {
            const binArrayAddr = successTx6BinArrays[i];
            const accountInfo = await connection.getAccountInfo(new PublicKey(binArrayAddr));
            console.log(`     Bin Array ${i + 1}: ${binArrayAddr}`);
            console.log(`     Существует: ${accountInfo ? '✅ ДА' : '❌ НЕТ'}`);
            
            if (accountInfo) {
                try {
                    const data = accountInfo.data;
                    let offset = 8; // skip discriminator
                    const version = data.readUInt8(offset);
                    offset += 1;
                    const index = data.readBigInt64LE(offset);
                    console.log(`     Index: ${index}`);
                } catch (e) {
                    console.log(`     Не удалось прочитать index`);
                }
            }
        }

        // Успешная транзакция #7
        const successTx7BinArray = '7YLfregNFN6jihCbcBSCLdwVUKh7aM78znyUHfCj4kbf'; // #15
        
        console.log('\n   Успешная транзакция #7:');
        const tx7AccountInfo = await connection.getAccountInfo(new PublicKey(successTx7BinArray));
        console.log(`     Bin Array: ${successTx7BinArray}`);
        console.log(`     Существует: ${tx7AccountInfo ? '✅ ДА' : '❌ НЕТ'}`);
        
        if (tx7AccountInfo) {
            try {
                const data = tx7AccountInfo.data;
                let offset = 8; // skip discriminator
                const version = data.readUInt8(offset);
                offset += 1;
                const index = data.readBigInt64LE(offset);
                console.log(`     Index: ${index}`);
            } catch (e) {
                console.log(`     Не удалось прочитать index`);
            }
        }

        // ВЫВОДЫ
        console.log('\n🎯 ВЫВОДЫ:');
        console.log('1. Failed транзакция имеет только 1 bin array');
        console.log('2. Успешная транзакция #6 имеет 2 bin arrays');
        console.log('3. Успешная транзакция #7 имеет 1 bin array');
        console.log('');
        console.log('🚨 ПРОБЛЕМА: Наш код создает неправильное количество bin arrays!');
        console.log('💡 РЕШЕНИЕ: Нужно проанализировать какие именно bin arrays требуются');

        // ПРОВЕРЯЕМ АКТИВНЫЙ БИН
        console.log('\n🔍 ПРОВЕРКА АКТИВНОГО БИНА:');
        try {
            // Получаем информацию о LB Pair
            const lbPairInfo = await connection.getAccountInfo(new PublicKey(lbPair));
            if (lbPairInfo) {
                console.log(`   LB Pair размер: ${lbPairInfo.data.length} байт`);
                
                // Пытаемся найти активный bin ID
                // Это требует знания структуры LB Pair
                console.log('   Активный bin ID: ТРЕБУЕТ АНАЛИЗА СТРУКТУРЫ LB PAIR');
            }
        } catch (e) {
            console.log(`   Ошибка анализа LB Pair: ${e.message}`);
        }

        return {
            failedBinArray,
            ourBinArrayIndices: Array.from(ourBinArrayIndices),
            successTx6BinArrays,
            successTx7BinArray
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        console.error(error.stack);
        return null;
    }
}

// Запуск анализа
if (require.main === module) {
    analyzeFailedTransaction()
        .then(result => {
            if (result) {
                console.log('\n✅ АНАЛИЗ ЗАВЕРШЕН!');
                console.log('📋 Результаты сохранены для дальнейшего исследования');
            } else {
                console.log('\n❌ АНАЛИЗ НЕ УДАЛСЯ!');
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
        });
}

module.exports = { analyzeFailedTransaction };
