/**
 * 🔥 ПОИСК ПРАВИЛЬНОГО LB PAIR ДЛЯ WSOL-USDC
 * ПРОВЕРЯЕМ СУЩЕСТВУЮЩИЕ METEORA ПУЛЫ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function findCorrectLBPair() {
    console.log('🔥 ПОИСК ПРАВИЛЬНОГО LB PAIR ДЛЯ WSOL-USDC...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // Meteora DLMM Program ID
        const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Token Mints
        const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
        const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

        console.log('📊 ПОИСК METEORA LB PAIRS...');
        console.log(`   WSOL: ${WSOL_MINT.toString()}`);
        console.log(`   USDC: ${USDC_MINT.toString()}\n`);

        // Ищем все LB Pairs в Meteora программе
        const lbPairs = await connection.getProgramAccounts(METEORA_PROGRAM_ID, {
            filters: [
                {
                    dataSize: 1000 // Примерный размер LB Pair аккаунта
                }
            ]
        });

        console.log(`🔍 Найдено ${lbPairs.length} аккаунтов Meteora программы\n`);

        // Проверяем каждый аккаунт
        let foundPairs = [];
        
        for (let i = 0; i < Math.min(lbPairs.length, 20); i++) { // Ограничиваем до 20 для теста
            const account = lbPairs[i];
            
            try {
                console.log(`📊 Проверяем аккаунт ${i + 1}/${Math.min(lbPairs.length, 20)}: ${account.pubkey.toString()}`);
                
                // Проверяем размер данных
                const dataSize = account.account.data.length;
                console.log(`   Размер данных: ${dataSize} байт`);
                
                // Если размер подходит для LB Pair (обычно около 1000+ байт)
                if (dataSize > 500 && dataSize < 2000) {
                    console.log(`   ✅ Подходящий размер для LB Pair!`);
                    
                    // Пытаемся прочитать первые байты для поиска token mints
                    const data = account.account.data;
                    
                    // Ищем WSOL и USDC mint адреса в данных
                    const dataHex = data.toString('hex');
                    const wsolHex = WSOL_MINT.toBuffer().toString('hex');
                    const usdcHex = USDC_MINT.toBuffer().toString('hex');
                    
                    if (dataHex.includes(wsolHex) && dataHex.includes(usdcHex)) {
                        console.log(`   🎯 НАЙДЕН WSOL-USDC PAIR!`);
                        foundPairs.push({
                            address: account.pubkey.toString(),
                            dataSize: dataSize,
                            lamports: account.account.lamports
                        });
                    } else {
                        console.log(`   ❌ Не содержит WSOL-USDC`);
                    }
                } else {
                    console.log(`   ❌ Неподходящий размер`);
                }
                
                console.log('');
                
                // Небольшая задержка
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`   💥 Ошибка: ${error.message}\n`);
            }
        }

        // РЕЗУЛЬТАТЫ
        console.log('🎯 РЕЗУЛЬТАТЫ ПОИСКА:');
        if (foundPairs.length > 0) {
            console.log(`   ✅ Найдено ${foundPairs.length} WSOL-USDC пар:`);
            foundPairs.forEach((pair, index) => {
                console.log(`   ${index + 1}. ${pair.address}`);
                console.log(`      Размер: ${pair.dataSize} байт`);
                console.log(`      Lamports: ${pair.lamports}`);
            });
        } else {
            console.log(`   ❌ WSOL-USDC пары не найдены в первых 20 аккаунтах`);
        }

        // ПРОВЕРЯЕМ ИЗВЕСТНЫЕ LB PAIRS
        console.log('\n🔍 ПРОВЕРЯЕМ ИЗВЕСТНЫЕ LB PAIRS:');
        
        const knownPairs = [
            '5rCf1DM8K7CDTpGG6b9JTVS8n7eD4BD2Pg4YBNGZzvgF', // Из кэша
            'BGm1tav5K4NjMXVVS6vGHPdJhZLhzNNdvCNvgqjYqiG8', // Из ошибки
            '7YLfregNFN6jihCbcBSCLdwVUKh7aM78znyUHfCj4kbf'  // Другой из ошибки
        ];

        for (const pairAddress of knownPairs) {
            try {
                console.log(`📊 Проверяем: ${pairAddress}`);
                const publicKey = new PublicKey(pairAddress);
                const accountInfo = await connection.getAccountInfo(publicKey);
                
                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   📊 Owner: ${accountInfo.owner.toString()}`);
                    
                    if (accountInfo.owner.equals(METEORA_PROGRAM_ID)) {
                        console.log(`   🎯 ПРИНАДЛЕЖИТ METEORA ПРОГРАММЕ!`);
                    }
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ`);
                }
                console.log('');
            } catch (error) {
                console.log(`   💥 Ошибка: ${error.message}\n`);
            }
        }

        return foundPairs;

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск поиска
if (require.main === module) {
    findCorrectLBPair()
        .then(result => {
            if (result && result.length > 0) {
                console.log('\n✅ НАЙДЕНЫ LB PAIRS!');
                process.exit(0);
            } else {
                console.log('\n❌ LB PAIRS НЕ НАЙДЕНЫ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { findCorrectLBPair };
