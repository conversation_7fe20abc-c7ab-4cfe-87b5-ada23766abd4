/**
 * 🔍 ПРОДВИНУТЫЙ АНАЛИЗАТОР АККАУНТОВ ДЛЯ ALT ТАБЛИЦЫ
 * ПОЛНАЯ ДЕТАЛИЗАЦИЯ: НАЗВАНИЯ, ТИПЫ, СЖИМАЕМОСТЬ, ИСПОЛЬЗОВАНИЕ
 */

const { PublicKey } = require('@solana/web3.js');

/**
 * 📋 ДЕТАЛЬНЫЙ АНАЛИЗ ВСЕХ АККАУНТОВ
 */
const ACCOUNT_ANALYSIS = {
    
    // 🔧 PROGRAM IDS
    PROGRAMS: {
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': {
            name: 'Meteora DLMM Program',
            type: 'PROGRAM',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2', 'remove_liquidity'],
            role: 'Main program for Meteora DLMM operations',
            dynamic: false,
            signer: false,
            category: 'CORE_PROGRAM'
        },
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': {
            name: 'SPL Token Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2', 'token_transfers'],
            role: 'Token operations (transfer, mint, burn)',
            dynamic: false,
            signer: false,
            category: 'SYSTEM_PROGRAM'
        },
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': {
            name: 'Associated Token Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['token_account_creation'],
            role: 'Create associated token accounts',
            dynamic: false,
            signer: false,
            category: 'SYSTEM_PROGRAM'
        },
        '11111111111111111111111111111111': {
            name: 'System Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['account_creation', 'lamport_transfers'],
            role: 'Core Solana system operations',
            dynamic: false,
            signer: false,
            category: 'SYSTEM_PROGRAM'
        },
        'ComputeBudget111111111111111111111111111111': {
            name: 'Compute Budget Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['compute_budget_instructions'],
            role: 'Set compute unit limits and prices',
            dynamic: false,
            signer: false,
            category: 'SYSTEM_PROGRAM'
        },
        'Sysvar1nstructions1111111111111111111111111': {
            name: 'Sysvar Instructions',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['instruction_introspection'],
            role: 'Access to current instruction data',
            dynamic: false,
            signer: false,
            category: 'SYSVAR'
        },
        'SysvarRent111111111111111111111111111111111': {
            name: 'Sysvar Rent',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['rent_calculations'],
            role: 'Rent exemption calculations',
            dynamic: false,
            signer: false,
            category: 'SYSVAR'
        },
        'SysvarC1ock11111111111111111111111111111111': {
            name: 'Sysvar Clock',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['timestamp_operations'],
            role: 'Current timestamp and slot info',
            dynamic: false,
            signer: false,
            category: 'SYSVAR'
        },
        // 🗑️ JUPITER УДАЛЕН - УЖЕ ЕСТЬ В CUSTOM ALT ТАБЛИЦЕ!
        'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': {
            name: 'MarginFi Program',
            type: 'PROGRAM',
            canCompress: true,
            usedIn: ['flash_loans'],
            role: 'Flash loan provider',
            dynamic: false,
            signer: false,
            category: 'EXTERNAL_PROGRAM'
        }
    },

    // 💰 TOKEN MINTS
    MINTS: {
        'So11111111111111111111111111111111111111112': {
            name: 'WSOL (Wrapped SOL)',
            type: 'TOKEN_MINT',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'Wrapped SOL token mint',
            dynamic: false,
            signer: false,
            category: 'TOKEN_MINT'
        },
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': {
            name: 'USDC',
            type: 'TOKEN_MINT',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'USDC token mint',
            dynamic: false,
            signer: false,
            category: 'TOKEN_MINT'
        },
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': {
            name: 'USDT',
            type: 'TOKEN_MINT',
            canCompress: true,
            usedIn: ['potential_arbitrage'],
            role: 'USDT token mint',
            dynamic: false,
            signer: false,
            category: 'TOKEN_MINT'
        }
    },

    // 🏊 METEORA POOLS
    POOLS: {
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': {
            name: 'Meteora Pool 1 (WSOL-USDC)',
            type: 'METEORA_POOL',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'Main trading pool for WSOL-USDC',
            dynamic: false,
            signer: false,
            category: 'METEORA_POOL'
        },
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y': {
            name: 'Meteora Pool 2 (WSOL-USDC)',
            type: 'METEORA_POOL',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'Secondary trading pool for WSOL-USDC',
            dynamic: false,
            signer: false,
            category: 'METEORA_POOL'
        }
    },

    // 🎯 METEORA POSITIONS (СТАТИЧЕСКИЕ)
    POSITIONS: {
        'AegDe4QfxF48RxTXowrcH9yfFR7873WkC9WDKwmJjX2a': {
            name: 'Position Pool 1',
            type: 'METEORA_POSITION',
            canCompress: true,
            usedIn: ['add_liquidity2', 'claim_fee2', 'remove_liquidity'],
            role: 'NFT position for Pool 1',
            dynamic: false,
            signer: false,
            category: 'METEORA_POSITION'
        },
        'BuKyq1rw3rFQX3JRbVqMcQAcyNs4arTBCWLvQGnu6nKJ': {
            name: 'Position Pool 2',
            type: 'METEORA_POSITION',
            canCompress: true,
            usedIn: ['add_liquidity2', 'claim_fee2', 'remove_liquidity'],
            role: 'NFT position for Pool 2',
            dynamic: false,
            signer: false,
            category: 'METEORA_POSITION'
        }
    },

    // 🏦 RESERVES
    RESERVES: {
        'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF': {
            name: 'Pool 1 Reserve X (WSOL)',
            type: 'METEORA_RESERVE',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'WSOL reserve for Pool 1',
            dynamic: false,
            signer: false,
            category: 'METEORA_RESERVE'
        },
        'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD': {
            name: 'Pool 1 Reserve Y (USDC)',
            type: 'METEORA_RESERVE',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'USDC reserve for Pool 1',
            dynamic: false,
            signer: false,
            category: 'METEORA_RESERVE'
        },
        'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq': {
            name: 'Pool 2 Reserve X (WSOL)',
            type: 'METEORA_RESERVE',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'WSOL reserve for Pool 2',
            dynamic: false,
            signer: false,
            category: 'METEORA_RESERVE'
        },
        'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi': {
            name: 'Pool 2 Reserve Y (USDC)',
            type: 'METEORA_RESERVE',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'USDC reserve for Pool 2',
            dynamic: false,
            signer: false,
            category: 'METEORA_RESERVE'
        }
    },

    // 🗃️ BIN ARRAYS (ДИНАМИЧЕСКИЕ)
    BIN_ARRAYS: {
        '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF': {
            name: 'Bin Array Index -65',
            type: 'METEORA_BIN_ARRAY',
            canCompress: false, // ❌ ДИНАМИЧЕСКИЕ BIN ARRAYS НЕ СЖИМАЮТСЯ!
            usedIn: ['add_liquidity2', 'swap'],
            role: 'Price bins for index -65',
            dynamic: true, // 🔄 ЗАВИСИТ ОТ АКТИВНОГО BIN ID!
            signer: false,
            category: 'METEORA_BIN_ARRAY'
        },
        '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4': {
            name: 'Bin Array Index -64',
            type: 'METEORA_BIN_ARRAY',
            canCompress: false, // ❌ ДИНАМИЧЕСКИЕ BIN ARRAYS НЕ СЖИМАЮТСЯ!
            usedIn: ['add_liquidity2', 'swap'],
            role: 'Price bins for index -64',
            dynamic: true, // 🔄 ЗАВИСИТ ОТ АКТИВНОГО BIN ID!
            signer: false,
            category: 'METEORA_BIN_ARRAY'
        }
    },

    // 🔧 BITMAP EXTENSIONS
    BITMAP_EXTENSIONS: {
        'DArpuuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE': {
            name: 'Pool 1 Bitmap Extension',
            type: 'METEORA_BITMAP_EXTENSION',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap'],
            role: 'Bitmap extension for Pool 1',
            dynamic: false,
            signer: false,
            category: 'METEORA_BITMAP_EXTENSION'
        }
    },

    // 🔮 ORACLES
    ORACLES: {
        '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li': {
            name: 'Pool 1 Oracle',
            type: 'METEORA_ORACLE',
            canCompress: true,
            usedIn: ['swap', 'price_calculations'],
            role: 'Price oracle for Pool 1',
            dynamic: false,
            signer: false,
            category: 'METEORA_ORACLE'
        },
        'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj': {
            name: 'Pool 2 Oracle',
            type: 'METEORA_ORACLE',
            canCompress: true,
            usedIn: ['swap', 'price_calculations'],
            role: 'Price oracle for Pool 2',
            dynamic: false,
            signer: false,
            category: 'METEORA_ORACLE'
        }
    },

    // 🎫 EVENT AUTHORITY
    EVENT_AUTHORITY: {
        'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6': {
            name: 'Meteora Event Authority',
            type: 'METEORA_EVENT_AUTHORITY',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2', 'remove_liquidity'],
            role: 'Event logging for Meteora operations',
            dynamic: false,
            signer: false,
            category: 'METEORA_EVENT_AUTHORITY'
        }
    },

    // 🏪 MARGINFI
    MARGINFI: {
        '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8': {
            name: 'MarginFi Group',
            type: 'MARGINFI_GROUP',
            canCompress: true,
            usedIn: ['flash_loans'],
            role: 'MarginFi lending group',
            dynamic: false,
            signer: false,
            category: 'MARGINFI'
        }
    },

    // 👤 USER ACCOUNTS (НЕ СЖИМАЮТСЯ - ДИНАМИЧЕСКИЕ И ПОДПИСЫВАЮЩИЕ)
    USER_ACCOUNTS: {
        'USER_WALLET': {
            name: 'User Wallet',
            type: 'USER_WALLET',
            canCompress: false, // ❌ ПОДПИСЫВАЮЩИЕ АККАУНТЫ НЕ СЖИМАЮТСЯ!
            usedIn: ['all_instructions'],
            role: 'User wallet (signer)',
            dynamic: true, // 🔄 УНИКАЛЕН ДЛЯ КАЖДОГО ПОЛЬЗОВАТЕЛЯ!
            signer: true, // ✍️ ПОДПИСЫВАЮЩИЙ АККАУНТ!
            category: 'USER_ACCOUNT'
        },
        'USER_WSOL_ATA': {
            name: 'User WSOL Token Account',
            type: 'USER_TOKEN_ACCOUNT',
            canCompress: false, // ❌ ПОЛЬЗОВАТЕЛЬСКИЕ АККАУНТЫ НЕ СЖИМАЮТСЯ!
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'User WSOL associated token account',
            dynamic: true, // 🔄 УНИКАЛЕН ДЛЯ КАЖДОГО ПОЛЬЗОВАТЕЛЯ!
            signer: false,
            category: 'USER_ACCOUNT'
        },
        'USER_USDC_ATA': {
            name: 'User USDC Token Account',
            type: 'USER_TOKEN_ACCOUNT',
            canCompress: false, // ❌ ПОЛЬЗОВАТЕЛЬСКИЕ АККАУНТЫ НЕ СЖИМАЮТСЯ!
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2'],
            role: 'User USDC associated token account',
            dynamic: true, // 🔄 УНИКАЛЕН ДЛЯ КАЖДОГО ПОЛЬЗОВАТЕЛЯ!
            signer: false,
            category: 'USER_ACCOUNT'
        }
    }
};

/**
 * 🔍 ЗАГРУЗКА СУЩЕСТВУЮЩЕЙ CUSTOM ALT ТАБЛИЦЫ
 */
function loadExistingCustomALT() {
    try {
        const fs = require('fs');
        const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));

        if (altData.tables && altData.tables.custom && altData.tables.custom.addresses) {
            console.log(`📋 ЗАГРУЖЕНА СУЩЕСТВУЮЩАЯ CUSTOM ALT: ${altData.tables.custom.addresses.length} адресов`);
            return new Set(altData.tables.custom.addresses);
        }

        if (altData.custom && altData.custom.addresses) {
            console.log(`📋 ЗАГРУЖЕНА СУЩЕСТВУЮЩАЯ CUSTOM ALT: ${altData.custom.addresses.length} адресов`);
            return new Set(altData.custom.addresses);
        }

        console.log(`⚠️ Custom ALT таблица не найдена в файле`);
        return new Set();

    } catch (error) {
        console.log(`⚠️ Ошибка загрузки custom-alt-data.json: ${error.message}`);
        return new Set();
    }
}

/**
 * 🔍 АНАЛИЗ АККАУНТОВ ДЛЯ ALT СЖАТИЯ С ПРОВЕРКОЙ ДУБЛЕЙ
 */
function analyzeAccountsForALT() {
    console.log('🔍 ПРОДВИНУТЫЙ АНАЛИЗ АККАУНТОВ ДЛЯ ALT ТАБЛИЦЫ');
    console.log('='.repeat(60));

    // Загружаем существующую custom ALT таблицу
    const existingCustomALT = loadExistingCustomALT();

    const compressibleAccounts = [];
    const nonCompressibleAccounts = [];
    const dynamicAccounts = [];
    const signerAccounts = [];
    const duplicateAccounts = [];
    const newAccounts = [];

    // Анализируем все категории
    const allCategories = Object.values(ACCOUNT_ANALYSIS);

    allCategories.forEach(category => {
        Object.entries(category).forEach(([address, info]) => {
            const accountData = {
                address: address,
                ...info
            };

            // Проверяем дублирование с существующей custom ALT
            if (existingCustomALT.has(address)) {
                duplicateAccounts.push(accountData);
                console.log(`   🔄 ДУБЛЬ НАЙДЕН: ${info.name} (${address.slice(0,8)}...)`);
            } else {
                newAccounts.push(accountData);
            }

            if (info.canCompress) {
                compressibleAccounts.push(accountData);
            } else {
                nonCompressibleAccounts.push(accountData);
            }

            if (info.dynamic) {
                dynamicAccounts.push(accountData);
            }

            if (info.signer) {
                signerAccounts.push(accountData);
            }
        });
    });

    // Выводим результаты анализа
    console.log('\\n📊 СТАТИСТИКА АНАЛИЗА:');
    console.log(`   ✅ Сжимаемые аккаунты: ${compressibleAccounts.length}`);
    console.log(`   ❌ НЕ сжимаемые аккаунты: ${nonCompressibleAccounts.length}`);
    console.log(`   🔄 Динамические аккаунты: ${dynamicAccounts.length}`);
    console.log(`   ✍️  Подписывающие аккаунты: ${signerAccounts.length}`);
    console.log(`   🔄 ДУБЛИ с custom ALT: ${duplicateAccounts.length}`);
    console.log(`   🆕 НОВЫЕ аккаунты: ${newAccounts.length}`);

    // Детальный анализ по категориям
    console.log('\\n🔧 ДЕТАЛЬНЫЙ АНАЛИЗ ПО КАТЕГОРИЯМ:');

    const categories = {};
    compressibleAccounts.forEach(acc => {
        if (!categories[acc.category]) {
            categories[acc.category] = [];
        }
        categories[acc.category].push(acc);
    });

    Object.entries(categories).forEach(([category, accounts]) => {
        console.log(`\\n📋 ${category} (${accounts.length} аккаунтов):`);
        accounts.forEach((acc, index) => {
            console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${acc.name}`);
            console.log(`       Address: ${acc.address}`);
            console.log(`       Used in: ${acc.usedIn.join(', ')}`);
            console.log(`       Role: ${acc.role}`);
        });
    });

    // НЕ сжимаемые аккаунты
    console.log('\\n❌ НЕ СЖИМАЕМЫЕ АККАУНТЫ (ИСКЛЮЧАЕМ ИЗ ALT):');
    nonCompressibleAccounts.forEach((acc, index) => {
        const reason = [];
        if (acc.type === 'SYSVAR') reason.push('SYSVAR');
        if (acc.dynamic) reason.push('DYNAMIC');
        if (acc.signer) reason.push('SIGNER');
        if (acc.type === 'USER_WALLET' || acc.type === 'USER_TOKEN_ACCOUNT') reason.push('USER_SPECIFIC');

        console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${acc.name}`);
        console.log(`       Address: ${acc.address || 'DYNAMIC'}`);
        console.log(`       Причина: ${reason.join(', ')}`);
        console.log(`       Type: ${acc.type}`);
    });

    // Создаем финальный список для ALT
    console.log('\\n🔥 ФИНАЛЬНЫЙ СПИСОК ДЛЯ ALT ТАБЛИЦЫ:');
    console.log(`   📊 Всего аккаунтов для сжатия: ${compressibleAccounts.length}`);
    console.log(`   💾 Экономия байт на транзакцию: ${compressibleAccounts.length * 32} → ${compressibleAccounts.length} байт`);
    console.log(`   💰 Экономия: ${compressibleAccounts.length * 31} байт на транзакцию`);

    // Показываем дубли
    if (duplicateAccounts.length > 0) {
        console.log('\\n🔄 ДУБЛИ С СУЩЕСТВУЮЩЕЙ CUSTOM ALT (ИСКЛЮЧАЕМ):');
        duplicateAccounts.forEach((acc, index) => {
            console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${acc.name} (${acc.address.slice(0,8)}...)`);
        });
    }

    // Показываем только новые аккаунты для добавления
    const newCompressibleAccounts = compressibleAccounts.filter(acc => !existingCustomALT.has(acc.address));

    console.log('\\n🆕 НОВЫЕ АККАУНТЫ ДЛЯ ДОБАВЛЕНИЯ В ALT:');
    console.log(`   📊 Всего новых сжимаемых: ${newCompressibleAccounts.length}`);

    if (newCompressibleAccounts.length > 0) {
        console.log('\\n📋 СПИСОК НОВЫХ АДРЕСОВ ДЛЯ ALT:');
        newCompressibleAccounts.forEach((acc, index) => {
            console.log(`   ${(index + 1).toString().padStart(3, ' ')}. ${acc.address} // ${acc.name}`);
        });
    } else {
        console.log('\\n✅ ВСЕ НУЖНЫЕ АККАУНТЫ УЖЕ ЕСТЬ В CUSTOM ALT ТАБЛИЦЕ!');
    }

    return {
        compressible: compressibleAccounts,
        nonCompressible: nonCompressibleAccounts,
        dynamic: dynamicAccounts,
        signers: signerAccounts
    };
}

/**
 * 🔍 АНАЛИЗ ИСПОЛЬЗОВАНИЯ АККАУНТОВ ПО ИНСТРУКЦИЯМ
 */
function analyzeAccountsByInstruction() {
    console.log('\\n🎯 АНАЛИЗ ИСПОЛЬЗОВАНИЯ АККАУНТОВ ПО ИНСТРУКЦИЯМ:');
    console.log('='.repeat(60));

    const instructionMap = {};

    // Собираем все аккаунты по инструкциям
    Object.values(ACCOUNT_ANALYSIS).forEach(category => {
        Object.entries(category).forEach(([address, info]) => {
            info.usedIn.forEach(instruction => {
                if (!instructionMap[instruction]) {
                    instructionMap[instruction] = [];
                }
                instructionMap[instruction].push({
                    address: address,
                    name: info.name,
                    canCompress: info.canCompress,
                    dynamic: info.dynamic,
                    signer: info.signer,
                    category: info.category
                });
            });
        });
    });

    // Выводим анализ по инструкциям
    Object.entries(instructionMap).forEach(([instruction, accounts]) => {
        const compressible = accounts.filter(acc => acc.canCompress).length;
        const total = accounts.length;

        console.log(`\\n📋 ${instruction.toUpperCase()} (${total} аккаунтов, ${compressible} сжимаемых):`);

        accounts.forEach((acc, index) => {
            const status = acc.canCompress ? '✅' : '❌';
            const flags = [];
            if (acc.dynamic) flags.push('DYNAMIC');
            if (acc.signer) flags.push('SIGNER');

            console.log(`   ${status} ${(index + 1).toString().padStart(2, ' ')}. ${acc.name}`);
            if (flags.length > 0) {
                console.log(`       Flags: ${flags.join(', ')}`);
            }
        });
    });
}

/**
 * 💾 ЭКСПОРТ ДЛЯ ИСПОЛЬЗОВАНИЯ В ДРУГИХ ФАЙЛАХ
 */
module.exports = {
    ACCOUNT_ANALYSIS,
    analyzeAccountsForALT,
    analyzeAccountsByInstruction
};

// 🔥 ЕСЛИ ЗАПУСКАЕТСЯ НАПРЯМУЮ - ПОКАЗЫВАЕМ ПОЛНЫЙ АНАЛИЗ
if (require.main === module) {
    const analysis = analyzeAccountsForALT();
    analyzeAccountsByInstruction();

    console.log('\\n🎯 АНАЛИЗ ЗАВЕРШЕН!');
    console.log(`   ✅ Готово к созданию ALT таблицы с ${analysis.compressible.length} аккаунтами`);
}
