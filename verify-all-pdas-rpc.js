/**
 * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ ВСЕХ PDA ЧЕРЕЗ RPC
 */

const { Connection, PublicKey } = require('@solana/web3.js');

// RPC подключение
const connection = new Connection('https://solana-mainnet.api.syndica.io/api-key/2k9VGQQAaaduhNKRVzLABzjzbFVcK9WpMynpaVEkLgcUdfdEaVsEUA3CQ1WiFVThGEBCaGtz2269oPXPDwaRkNdgeL3inNgtpvU');

// Все PDA для проверки
const PDA_TO_CHECK = {
    // Position PDA (из trading-config.js)
    POSITION_POOL_1: 'Gbv33r6KGduHGWb8D5m4jhJMTqnGE4oaMun7ToHLr1UC',
    POSITION_POOL_2: 'Axf1TsquSoML5qJ7QyM1jxLPbHRazc9r4xBYjcADnz3S',
    
    // Reserve PDA (исправленные адреса из успешной транзакции)
    RESERVE_X_POOL_1: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF',
    RESERVE_Y_POOL_1: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD',
    RESERVE_X_POOL_2: 'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq',
    RESERVE_Y_POOL_2: 'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi',
    
    // Event Authority (правильная формула)
    EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
    
    // Bin Arrays (найдены автоматическим перебором)
    BIN_ARRAY_1: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // binArrayIndex: -65
    BIN_ARRAY_2: '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4', // binArrayIndex: -64
    
    // Bitmap Extension (точный адрес из успешной транзакции)
    BITMAP_EXTENSION: 'DArpuuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE'
};

// Ожидаемые владельцы
const EXPECTED_OWNERS = {
    METEORA_PROGRAM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    SYSTEM_PROGRAM: '11111111111111111111111111111111'
};

/**
 * 🔍 Проверка одного PDA
 */
async function checkPDA(name, address, expectedOwner = EXPECTED_OWNERS.METEORA_PROGRAM) {
    try {
        console.log(`\n🔍 Проверяем ${name}:`);
        console.log(`   Адрес: ${address}`);
        
        const pubkey = new PublicKey(address);
        const accountInfo = await connection.getAccountInfo(pubkey);
        
        if (!accountInfo) {
            console.log(`   ❌ АККАУНТ НЕ СУЩЕСТВУЕТ`);
            return {
                name,
                address,
                exists: false,
                owner: null,
                lamports: 0,
                dataLength: 0,
                valid: false
            };
        }
        
        const owner = accountInfo.owner.toBase58();
        const lamports = accountInfo.lamports;
        const dataLength = accountInfo.data.length;
        const isValidOwner = owner === expectedOwner;
        
        console.log(`   ✅ АККАУНТ СУЩЕСТВУЕТ`);
        console.log(`   💰 Lamports: ${lamports.toLocaleString()}`);
        console.log(`   📊 Data Length: ${dataLength} bytes`);
        console.log(`   👤 Owner: ${owner}`);
        console.log(`   🎯 Expected Owner: ${expectedOwner}`);
        console.log(`   ✅ Valid Owner: ${isValidOwner ? 'ДА' : 'НЕТ'}`);
        
        if (!isValidOwner) {
            console.log(`   ⚠️ НЕПРАВИЛЬНЫЙ ВЛАДЕЛЕЦ!`);
        }
        
        return {
            name,
            address,
            exists: true,
            owner,
            lamports,
            dataLength,
            valid: isValidOwner,
            expectedOwner
        };
        
    } catch (error) {
        console.log(`   💥 ОШИБКА ПРОВЕРКИ: ${error.message}`);
        return {
            name,
            address,
            exists: false,
            owner: null,
            lamports: 0,
            dataLength: 0,
            valid: false,
            error: error.message
        };
    }
}

/**
 * 🚀 Проверка всех PDA
 */
async function verifyAllPDAs() {
    console.log('🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ ВСЕХ PDA ЧЕРЕЗ RPC');
    console.log('='.repeat(60));
    console.log(`🌐 RPC: ${connection.rpcEndpoint}`);
    console.log(`📊 Всего PDA для проверки: ${Object.keys(PDA_TO_CHECK).length}`);
    
    const results = [];
    
    // Проверяем Position PDA
    console.log('\n📍 POSITION PDA:');
    results.push(await checkPDA('Position Pool 1', PDA_TO_CHECK.POSITION_POOL_1));
    results.push(await checkPDA('Position Pool 2', PDA_TO_CHECK.POSITION_POOL_2));
    
    // Проверяем Reserve PDA
    console.log('\n🏦 RESERVE PDA:');
    results.push(await checkPDA('Reserve X Pool 1', PDA_TO_CHECK.RESERVE_X_POOL_1));
    results.push(await checkPDA('Reserve Y Pool 1', PDA_TO_CHECK.RESERVE_Y_POOL_1));
    results.push(await checkPDA('Reserve X Pool 2', PDA_TO_CHECK.RESERVE_X_POOL_2));
    results.push(await checkPDA('Reserve Y Pool 2', PDA_TO_CHECK.RESERVE_Y_POOL_2));
    
    // Проверяем Event Authority
    console.log('\n🎫 EVENT AUTHORITY:');
    results.push(await checkPDA('Event Authority', PDA_TO_CHECK.EVENT_AUTHORITY));
    
    // Проверяем Bin Arrays
    console.log('\n🗃️ BIN ARRAYS:');
    results.push(await checkPDA('Bin Array 1 (-65)', PDA_TO_CHECK.BIN_ARRAY_1));
    results.push(await checkPDA('Bin Array 2 (-64)', PDA_TO_CHECK.BIN_ARRAY_2));
    
    // Проверяем Bitmap Extension
    console.log('\n🗺️ BITMAP EXTENSION:');
    results.push(await checkPDA('Bitmap Extension', PDA_TO_CHECK.BITMAP_EXTENSION));
    
    // Итоговая статистика
    console.log('\n' + '='.repeat(60));
    console.log('📊 ИТОГОВАЯ СТАТИСТИКА:');
    
    const existingCount = results.filter(r => r.exists).length;
    const validCount = results.filter(r => r.valid).length;
    const totalCount = results.length;
    
    console.log(`\n📈 ОБЩИЕ РЕЗУЛЬТАТЫ:`);
    console.log(`   📊 Всего проверено: ${totalCount}`);
    console.log(`   ✅ Существуют: ${existingCount}/${totalCount} (${(existingCount/totalCount*100).toFixed(1)}%)`);
    console.log(`   🎯 Правильный владелец: ${validCount}/${totalCount} (${(validCount/totalCount*100).toFixed(1)}%)`);
    
    // Детальная статистика по категориям
    const categories = {
        'Position PDA': results.slice(0, 2),
        'Reserve PDA': results.slice(2, 6),
        'Event Authority': results.slice(6, 7),
        'Bin Arrays': results.slice(7, 9),
        'Bitmap Extension': results.slice(9, 10)
    };
    
    console.log(`\n📋 ДЕТАЛЬНАЯ СТАТИСТИКА:`);
    for (const [category, categoryResults] of Object.entries(categories)) {
        const categoryExists = categoryResults.filter(r => r.exists).length;
        const categoryValid = categoryResults.filter(r => r.valid).length;
        const categoryTotal = categoryResults.length;
        
        console.log(`   ${categoryExists === categoryTotal ? '✅' : '❌'} ${category}: ${categoryExists}/${categoryTotal} существуют, ${categoryValid}/${categoryTotal} валидны`);
    }
    
    // Проблемные PDA
    const problematicPDAs = results.filter(r => !r.exists || !r.valid);
    if (problematicPDAs.length > 0) {
        console.log(`\n⚠️ ПРОБЛЕМНЫЕ PDA:`);
        problematicPDAs.forEach(pda => {
            if (!pda.exists) {
                console.log(`   ❌ ${pda.name}: НЕ СУЩЕСТВУЕТ`);
            } else if (!pda.valid) {
                console.log(`   ⚠️ ${pda.name}: НЕПРАВИЛЬНЫЙ ВЛАДЕЛЕЦ (${pda.owner})`);
            }
        });
    } else {
        console.log(`\n🎉 ВСЕ PDA СУЩЕСТВУЮТ И ВАЛИДНЫ!`);
    }
    
    return results;
}

// Запуск проверки
if (require.main === module) {
    verifyAllPDAs().then(results => {
        console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА!');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 ОШИБКА ПРОВЕРКИ:', error);
        process.exit(1);
    });
}

module.exports = { verifyAllPDAs, checkPDA, PDA_TO_CHECK };
