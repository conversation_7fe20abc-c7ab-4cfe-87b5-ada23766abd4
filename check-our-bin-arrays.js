/**
 * 🔍 ПРОВЕРЯЕМ КАКИЕ BIN ARRAYS МЫ ДОБАВЛЯЕМ В НАШЕЙ ТРАНЗАКЦИИ
 */

const { PublicKey } = require('@solana/web3.js');

function checkOurBinArrays() {
    console.log('🔍 ПРОВЕРЯЕМ НАШИ BIN ARRAYS...\n');

    // Наши текущие бины из кэша
    const ourBins = [-4576, -4575, -4574]; // Примерные значения
    
    console.log('📊 НАШИ БИНЫ:');
    ourBins.forEach((binId, index) => {
        console.log(`   ${index + 1}. Bin ID: ${binId}`);
    });

    console.log('\n🧮 ВЫЧИСЛЯЕМ BIN ARRAY INDICES:');
    
    // Правильная формула: Math.floor(binId / 64)
    const binArrayIndices = new Set();
    
    ourBins.forEach(binId => {
        const binArrayIndex = Math.floor(binId / 64);
        binArrayIndices.add(binArrayIndex);
        console.log(`   Bin ID ${binId} → Bin Array Index ${binArrayIndex}`);
    });

    console.log(`\n🎯 УНИКАЛЬНЫЕ BIN ARRAY INDICES: ${Array.from(binArrayIndices)}`);
    console.log(`📊 КОЛИЧЕСТВО BIN ARRAYS: ${binArrayIndices.size}`);

    // Проверяем диапазоны
    console.log('\n📋 ДИАПАЗОНЫ BIN ARRAYS:');
    for (const index of binArrayIndices) {
        const startBin = index * 64;
        const endBin = startBin + 63;
        console.log(`   Index ${index}: бины от ${startBin} до ${endBin}`);
        
        // Проверяем какие наши бины попадают в этот диапазон
        const binsInThisArray = ourBins.filter(binId => 
            binId >= startBin && binId <= endBin
        );
        console.log(`     Наши бины в этом массиве: ${binsInThisArray.join(', ')}`);
    }

    // Генерируем PDA для наших bin arrays
    console.log('\n🔧 ГЕНЕРИРУЕМ PDA ДЛЯ НАШИХ BIN ARRAYS:');
    
    const lbPair = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
    const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    
    const ourBinArrayPDAs = [];
    
    for (const index of binArrayIndices) {
        // Кодируем index как signed i64 LE
        const indexBuffer = Buffer.alloc(8);
        indexBuffer.writeBigInt64LE(BigInt(index), 0);
        
        const [binArrayPDA] = PublicKey.findProgramAddressSync([
            Buffer.from('bin_array'),
            lbPair.toBuffer(),
            indexBuffer
        ], METEORA_PROGRAM_ID);
        
        ourBinArrayPDAs.push(binArrayPDA);
        console.log(`   Index ${index}: ${binArrayPDA.toString()}`);
    }

    console.log('\n🎯 ИТОГ:');
    console.log(`✅ Наши бины попадают в ${binArrayIndices.size} bin array(s)`);
    console.log(`✅ Нужно добавить ${ourBinArrayPDAs.length} bin array PDA(s)`);
    
    if (binArrayIndices.size === 1) {
        console.log('✅ ВСЕ БИНЫ В ОДНОМ BIN ARRAY - ДОБАВЛЯЕМ ТОЛЬКО ОДИН!');
    } else {
        console.log('⚠️ БИНЫ РАСТЯНУТЫ НА НЕСКОЛЬКО BIN ARRAYS - ДОБАВЛЯЕМ ВСЕ!');
    }

    return {
        binArrayIndices: Array.from(binArrayIndices),
        binArrayPDAs: ourBinArrayPDAs,
        count: binArrayIndices.size
    };
}

// Запуск проверки
if (require.main === module) {
    const result = checkOurBinArrays();
    console.log('\n📊 РЕЗУЛЬТАТ:', result);
}

module.exports = { checkOurBinArrays };
