/**
 * 🔍 ПОИСК ПРАВИЛЬНЫХ RESERVE PDA ИЗ УСПЕШНЫХ SWAP ТРАНЗАКЦИЙ
 */

const { Connection, PublicKey } = require('@solana/web3.js');

// RPC подключение
const connection = new Connection('https://solana-mainnet.api.syndica.io/api-key/2k9VGQQAaaduhNKRVzLABzjzbFVcK9WpMynpaVEkLgcUdfdEaVsEUA3CQ1WiFVThGEBCaGtz2269oPXPDwaRkNdgeL3inNgtpvU');

// Известные успешные swap транзакции
const SUCCESSFUL_SWAP_SIGNATURES = [
    // Добавьте сюда подписи успешных swap транзакций
    '5c7XWQAKVX6huAd7Bo2YvY7NCVTK9xTPot53doxq8SypSLKViWjjn4jFys2C1nr8x8VfrBdWV4LfsERqRnKxm9A9'
];

// Reserve PDA из успешных swap транзакций (инструкции #8 и #9)
const SWAP_RESERVE_ADDRESSES = {
    // Из инструкции #8 (swap2)
    SWAP_RESERVE_X_1: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF', // #3 Reserve X
    SWAP_RESERVE_Y_1: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD', // #4 Reserve Y
    
    // Из инструкции #9 (swap2) - те же адреса
    SWAP_RESERVE_X_2: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF', // #3 Reserve X
    SWAP_RESERVE_Y_2: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD'  // #4 Reserve Y
};

// Константы
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
const LB_PAIR_POOL_1 = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
const LB_PAIR_POOL_2 = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

/**
 * 🔍 Проверка Reserve PDA из swap транзакций
 */
async function checkSwapReserves() {
    console.log('🔍 ПРОВЕРКА RESERVE PDA ИЗ УСПЕШНЫХ SWAP ТРАНЗАКЦИЙ');
    console.log('='.repeat(60));
    
    for (const [name, address] of Object.entries(SWAP_RESERVE_ADDRESSES)) {
        console.log(`\n🔍 Проверяем ${name}:`);
        console.log(`   Адрес: ${address}`);
        
        try {
            const pubkey = new PublicKey(address);
            const accountInfo = await connection.getAccountInfo(pubkey);
            
            if (!accountInfo) {
                console.log(`   ❌ АККАУНТ НЕ СУЩЕСТВУЕТ`);
                continue;
            }
            
            const owner = accountInfo.owner.toBase58();
            const lamports = accountInfo.lamports;
            const dataLength = accountInfo.data.length;
            
            console.log(`   ✅ АККАУНТ СУЩЕСТВУЕТ`);
            console.log(`   💰 Lamports: ${lamports.toLocaleString()}`);
            console.log(`   📊 Data Length: ${dataLength} bytes`);
            console.log(`   👤 Owner: ${owner}`);
            
            if (owner === METEORA_PROGRAM_ID.toBase58()) {
                console.log(`   ✅ ПРАВИЛЬНЫЙ ВЛАДЕЛЕЦ (Meteora Program)`);
            } else {
                console.log(`   ⚠️ НЕОЖИДАННЫЙ ВЛАДЕЛЕЦ`);
            }
            
        } catch (error) {
            console.log(`   💥 ОШИБКА: ${error.message}`);
        }
    }
}

/**
 * 🧮 Генерация Reserve PDA по формуле
 */
function generateReservePDA(lbPair, tokenMint) {
    const seeds = [
        Buffer.from('reserve'),
        lbPair.toBuffer(),
        tokenMint.toBuffer()
    ];
    
    return PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM_ID);
}

/**
 * 🔍 Проверка сгенерированных Reserve PDA
 */
async function checkGeneratedReserves() {
    console.log('\n🧮 ПРОВЕРКА СГЕНЕРИРОВАННЫХ RESERVE PDA ПО ФОРМУЛЕ');
    console.log('='.repeat(60));
    
    const reserves = [
        { name: 'Pool 1 Reserve X', lbPair: LB_PAIR_POOL_1, tokenMint: WSOL_MINT },
        { name: 'Pool 1 Reserve Y', lbPair: LB_PAIR_POOL_1, tokenMint: USDC_MINT },
        { name: 'Pool 2 Reserve X', lbPair: LB_PAIR_POOL_2, tokenMint: WSOL_MINT },
        { name: 'Pool 2 Reserve Y', lbPair: LB_PAIR_POOL_2, tokenMint: USDC_MINT }
    ];
    
    for (const reserve of reserves) {
        console.log(`\n🔍 Генерируем ${reserve.name}:`);
        
        try {
            const [pda, bump] = generateReservePDA(reserve.lbPair, reserve.tokenMint);
            console.log(`   📝 Сгенерированный PDA: ${pda.toBase58()}`);
            console.log(`   🎯 Bump: ${bump}`);
            
            const accountInfo = await connection.getAccountInfo(pda);
            
            if (!accountInfo) {
                console.log(`   ❌ АККАУНТ НЕ СУЩЕСТВУЕТ`);
                continue;
            }
            
            const owner = accountInfo.owner.toBase58();
            const lamports = accountInfo.lamports;
            const dataLength = accountInfo.data.length;
            
            console.log(`   ✅ АККАУНТ СУЩЕСТВУЕТ`);
            console.log(`   💰 Lamports: ${lamports.toLocaleString()}`);
            console.log(`   📊 Data Length: ${dataLength} bytes`);
            console.log(`   👤 Owner: ${owner}`);
            
            if (owner === METEORA_PROGRAM_ID.toBase58()) {
                console.log(`   ✅ ПРАВИЛЬНЫЙ ВЛАДЕЛЕЦ (Meteora Program)`);
            } else {
                console.log(`   ⚠️ НЕОЖИДАННЫЙ ВЛАДЕЛЕЦ`);
            }
            
        } catch (error) {
            console.log(`   💥 ОШИБКА: ${error.message}`);
        }
    }
}

/**
 * 🔍 Сравнение адресов
 */
function compareAddresses() {
    console.log('\n🔍 СРАВНЕНИЕ АДРЕСОВ RESERVE PDA');
    console.log('='.repeat(60));
    
    // Генерируем PDA по формуле
    const [pool1ReserveX] = generateReservePDA(LB_PAIR_POOL_1, WSOL_MINT);
    const [pool1ReserveY] = generateReservePDA(LB_PAIR_POOL_1, USDC_MINT);
    
    console.log(`\n📊 POOL 1 СРАВНЕНИЕ:`);
    console.log(`   Из swap транзакции Reserve X: ${SWAP_RESERVE_ADDRESSES.SWAP_RESERVE_X_1}`);
    console.log(`   Сгенерированный Reserve X:    ${pool1ReserveX.toBase58()}`);
    console.log(`   ✅ Совпадают: ${pool1ReserveX.toBase58() === SWAP_RESERVE_ADDRESSES.SWAP_RESERVE_X_1 ? 'ДА' : 'НЕТ'}`);
    
    console.log(`\n   Из swap транзакции Reserve Y: ${SWAP_RESERVE_ADDRESSES.SWAP_RESERVE_Y_1}`);
    console.log(`   Сгенерированный Reserve Y:    ${pool1ReserveY.toBase58()}`);
    console.log(`   ✅ Совпадают: ${pool1ReserveY.toBase58() === SWAP_RESERVE_ADDRESSES.SWAP_RESERVE_Y_1 ? 'ДА' : 'НЕТ'}`);
}

/**
 * 🚀 Главная функция
 */
async function main() {
    try {
        // 1. Проверяем Reserve из swap транзакций
        await checkSwapReserves();
        
        // 2. Проверяем сгенерированные Reserve PDA
        await checkGeneratedReserves();
        
        // 3. Сравниваем адреса
        compareAddresses();
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        
    } catch (error) {
        console.error('\n💥 ОШИБКА:', error);
    }
}

// Запуск
if (require.main === module) {
    main();
}

module.exports = { checkSwapReserves, checkGeneratedReserves, generateReservePDA };
