/**
 * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ 6 НОВЫХ АДРЕСОВ В БЛОКЧЕЙНЕ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function verifyNewAddresses() {
    console.log('🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ 6 НОВЫХ АДРЕСОВ В БЛОКЧЕЙНЕ');
    console.log('=' .repeat(80));

    // Подключение к RPC
    const connection = new Connection(process.env.SOLANA_RPC_URL);
    console.log('✅ Подключение к RPC установлено');

    // 6 новых адресов для проверки
    const addressesToVerify = [
        {
            address: 'AegDe4QfxF48RxTXowrcH9yfFR7873WkC9WDKwmJjX2a',
            name: 'Position Pool 1',
            type: 'METEORA_POSITION'
        },
        {
            address: 'BuKyq1rw3rFQX3JRbVqMcQAcyNs4arTBCWLvQGnu6nKJ',
            name: 'Position Pool 2',
            type: 'METEORA_POSITION'
        },
        {
            address: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF',
            name: 'Pool 1 Reserve X (WSOL)',
            type: 'METEORA_RESERVE'
        },
        {
            address: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD',
            name: 'Pool 1 Reserve Y (USDC)',
            type: 'METEORA_RESERVE'
        },
        {
            address: 'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq',
            name: 'Pool 2 Reserve X (WSOL)',
            type: 'METEORA_RESERVE'
        },
        {
            address: 'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi',
            name: 'Pool 2 Reserve Y (USDC)',
            type: 'METEORA_RESERVE'
        }
    ];

    console.log(`📋 Проверяем ${addressesToVerify.length} адресов...`);

    let validCount = 0;
    let invalidCount = 0;

    for (const item of addressesToVerify) {
        try {
            console.log(`\n🔍 Проверяем: ${item.name}`);
            console.log(`   Address: ${item.address}`);
            console.log(`   Type: ${item.type}`);

            // Проверяем валидность адреса
            const pubkey = new PublicKey(item.address);
            console.log(`   ✅ Адрес валиден`);

            // Проверяем существование в блокчейне
            const accountInfo = await connection.getAccountInfo(pubkey);

            if (accountInfo) {
                console.log(`   ✅ СУЩЕСТВУЕТ в блокчейне`);
                console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
                console.log(`   📊 Owner: ${accountInfo.owner.toString()}`);
                console.log(`   📊 Lamports: ${accountInfo.lamports}`);
                validCount++;
            } else {
                console.log(`   ❌ НЕ СУЩЕСТВУЕТ в блокчейне`);
                invalidCount++;
            }

        } catch (error) {
            console.log(`   💥 ОШИБКА: ${error.message}`);
            invalidCount++;
        }

        // Небольшая задержка между запросами
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    console.log(`\n${'='.repeat(80)}`);
    console.log('📊 РЕЗУЛЬТАТ ПРОВЕРКИ:');
    console.log(`   ✅ Валидные и существующие: ${validCount}`);
    console.log(`   ❌ Невалидные или несуществующие: ${invalidCount}`);
    console.log(`   📊 Процент успеха: ${Math.round((validCount / addressesToVerify.length) * 100)}%`);

    if (validCount === addressesToVerify.length) {
        console.log(`\n🎉 ВСЕ АДРЕСА ВАЛИДНЫ И СУЩЕСТВУЮТ В БЛОКЧЕЙНЕ!`);
        console.log(`✅ ГОТОВО К ДОБАВЛЕНИЮ В ALT ТАБЛИЦУ!`);
    } else {
        console.log(`\n⚠️ НЕКОТОРЫЕ АДРЕСА НЕ ПРОШЛИ ПРОВЕРКУ!`);
        console.log(`❌ ПРОВЕРЬТЕ АДРЕСА ПЕРЕД ДОБАВЛЕНИЕМ В ALT!`);
    }

    console.log(`${'='.repeat(80)}`);

    return {
        total: addressesToVerify.length,
        valid: validCount,
        invalid: invalidCount,
        success: validCount === addressesToVerify.length
    };
}

// Запуск проверки
if (require.main === module) {
    verifyNewAddresses().catch(console.error);
}

module.exports = { verifyNewAddresses };
