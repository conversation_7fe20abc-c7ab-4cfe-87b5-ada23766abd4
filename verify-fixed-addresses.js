/**
 * 🔥 ПРОВЕРКА ВСЕХ ФИКСИРОВАННЫХ METEORA АДРЕСОВ
 * ПРОВЕРЯЕМ ЧТО ВСЕ АДРЕСА СУЩЕСТВУЮТ И РАБОТАЮТ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function verifyFixedAddresses() {
    console.log('🔥 ПРОВЕРКА ВСЕХ ФИКСИРОВАННЫХ METEORA АДРЕСОВ...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // Получаем конфигурацию
        const meteoraConfig = rpcConfig.getMeteoraConfig();
        const pool1 = rpcConfig.getMeteoraPool1();
        const pool2 = rpcConfig.getMeteoraPool2();
        const eventAuthority = rpcConfig.getEventAuthority();

        console.log('📊 ПРОВЕРЯЕМ METEORA КОНФИГУРАЦИЮ:');
        console.log(`   Program ID: ${meteoraConfig.programId}`);
        console.log(`   Event Authority: ${eventAuthority}\n`);

        let totalChecked = 0;
        let totalExists = 0;
        const results = [];

        // Функция проверки аккаунта
        async function checkAccount(name, address, expectedOwner = null) {
            totalChecked++;
            console.log(`🔍 Проверяем: ${name}`);
            console.log(`   Адрес: ${address}`);

            try {
                const publicKey = new PublicKey(address);
                const accountInfo = await connection.getAccountInfo(publicKey);

                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   💰 Lamports: ${accountInfo.lamports}`);
                    console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);

                    if (expectedOwner && accountInfo.owner.toString() === expectedOwner) {
                        console.log(`   ✅ ПРАВИЛЬНЫЙ OWNER!`);
                    } else if (expectedOwner) {
                        console.log(`   ❌ НЕПРАВИЛЬНЫЙ OWNER! Ожидался: ${expectedOwner}`);
                    }

                    totalExists++;
                    results.push({ name, address, exists: true, size: accountInfo.data.length });
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ!`);
                    results.push({ name, address, exists: false });
                }
            } catch (error) {
                console.log(`   💥 ОШИБКА: ${error.message}`);
                results.push({ name, address, exists: false, error: error.message });
            }

            console.log('');
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ПРОВЕРЯЕМ POOL 1
        console.log('🔥 ПРОВЕРКА POOL 1:');
        await checkAccount('Pool 1 - LB Pair', pool1.lbPair, meteoraConfig.programId);
        await checkAccount('Pool 1 - Reserve X', pool1.reserveX, 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        await checkAccount('Pool 1 - Reserve Y', pool1.reserveY, 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

        // ПРОВЕРЯЕМ POOL 2
        console.log('🔥 ПРОВЕРКА POOL 2:');
        await checkAccount('Pool 2 - LB Pair', pool2.lbPair, meteoraConfig.programId);
        await checkAccount('Pool 2 - Reserve X', pool2.reserveX, 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        await checkAccount('Pool 2 - Reserve Y', pool2.reserveY, 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

        // ПРОВЕРЯЕМ EVENT AUTHORITY
        console.log('🔥 ПРОВЕРКА EVENT AUTHORITY:');
        await checkAccount('Event Authority', eventAuthority);

        // ПРОВЕРЯЕМ TOKEN MINTS
        console.log('🔥 ПРОВЕРКА TOKEN MINTS:');
        await checkAccount('WSOL Mint', meteoraConfig.tokens.WSOL);
        await checkAccount('USDC Mint', meteoraConfig.tokens.USDC);

        // ИТОГОВЫЙ ОТЧЁТ
        console.log('🎯 ИТОГОВЫЙ ОТЧЁТ:');
        console.log(`   ✅ Существующие аккаунты: ${totalExists}/${totalChecked}`);
        console.log(`   📊 Процент успеха: ${Math.round((totalExists / totalChecked) * 100)}%\n`);

        // ДЕТАЛЬНЫЙ ОТЧЁТ
        console.log('📋 ДЕТАЛЬНЫЙ ОТЧЁТ:');
        results.forEach((result, index) => {
            const status = result.exists ? '✅' : '❌';
            const size = result.size ? ` (${result.size} байт)` : '';
            console.log(`   ${index + 1}. ${status} ${result.name}${size}`);
            if (result.error) {
                console.log(`      💥 Ошибка: ${result.error}`);
            }
        });

        // АНАЛИЗ ПРОБЛЕМ
        const missing = results.filter(r => !r.exists);
        if (missing.length > 0) {
            console.log('\n❌ ОТСУТСТВУЮЩИЕ АККАУНТЫ:');
            missing.forEach((item, index) => {
                console.log(`   ${index + 1}. ${item.name}`);
                console.log(`      ${item.address}`);
            });

            console.log('\n🔧 РЕКОМЕНДАЦИИ:');
            console.log('   1. Проверить правильность адресов');
            console.log('   2. Убедиться что пулы не были закрыты');
            console.log('   3. Найти актуальные адреса через Meteora UI');
        } else {
            console.log('\n🎉 ВСЕ АДРЕСА КОРРЕКТНЫ И СУЩЕСТВУЮТ!');
        }

        return {
            totalChecked,
            totalExists,
            successRate: Math.round((totalExists / totalChecked) * 100),
            results,
            allValid: missing.length === 0
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск проверки
if (require.main === module) {
    verifyFixedAddresses()
        .then(result => {
            if (result && result.allValid) {
                console.log('\n✅ ВСЕ ФИКСИРОВАННЫЕ АДРЕСА КОРРЕКТНЫ!');
                process.exit(0);
            } else if (result) {
                console.log(`\n⚠️ НАЙДЕНЫ ПРОБЛЕМЫ! Успех: ${result.successRate}%`);
                process.exit(1);
            } else {
                console.log('\n💥 ОШИБКА ПРОВЕРКИ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { verifyFixedAddresses };
