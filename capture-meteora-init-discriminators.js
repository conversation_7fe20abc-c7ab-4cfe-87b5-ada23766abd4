/**
 * 🎯 ПЕРЕХВАТ METEORA INIT DISCRIMINATORS
 * Запускает Meteora SDK для создания позиции и перехватывает все init инструкции
 */

// 🔍 АКТИВИРУЕМ ПЕРЕХВАТЧИК ПЕРЕД ИМПОРТОМ SDK
const { startCapture, stopCapture, getResults, saveResults } = require('./meteora-init-discriminator-interceptor');

console.log('🚀 ЗАПУСК ПЕРЕХВАТА METEORA INIT DISCRIMINATORS');
console.log('='.repeat(60));

// Активируем перехватчик
startCapture();

async function captureInitDiscriminators() {
    try {
        console.log('📦 ИМПОРТ METEORA SDK...');
        
        // Импортируем после активации перехватчика
        const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
        const DLMM = require('@meteora-ag/dlmm').default;
        
        console.log('🌐 ПОДКЛЮЧЕНИЕ К RPC...');
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        
        // Создаем фейковый кошелек для тестов
        const fakeWallet = Keypair.generate();
        
        console.log('🎯 ПОЛУЧЕНИЕ DLMM INSTANCE...');
        const dlmmPubkey = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        const dlmm = await DLMM.create(connection, dlmmPubkey);
        
        console.log('💰 СОЗДАНИЕ ПОЗИЦИИ ДЛЯ ПЕРЕХВАТА INIT ИНСТРУКЦИЙ...');
        
        // Пытаемся создать позицию - это должно сгенерировать init инструкции
        try {
            const positionKeypair = Keypair.generate();
            
            // Параметры для создания позиции
            const createPositionParams = {
                positionKeypair,
                user: fakeWallet.publicKey,
                totalXAmount: new BN(1000000), // 1 WSOL
                totalYAmount: new BN(1000000), // 1 USDC
                strategy: {
                    strategyType: 'SpotBalanced',
                    positionWidth: 1
                }
            };
            
            console.log('🔨 ВЫЗОВ createPosition...');
            const createPositionTx = await dlmm.initializePosition(createPositionParams);
            
            console.log('✅ createPosition выполнен, анализируем перехваченные данные...');
            
        } catch (error) {
            console.log(`⚠️ Ошибка createPosition (ожидаемо): ${error.message}`);
            console.log('📊 Анализируем перехваченные данные...');
        }
        
        // Пытаемся создать add_liquidity транзакцию
        try {
            console.log('🔨 ВЫЗОВ addLiquidity для перехвата...');
            
            const addLiquidityParams = {
                positionPubKey: Keypair.generate().publicKey,
                totalXAmount: new BN(1000000),
                totalYAmount: new BN(1000000),
                strategy: {
                    strategyType: 'SpotBalanced',
                    positionWidth: 1
                }
            };
            
            const addLiquidityTx = await dlmm.addLiquidity(addLiquidityParams);
            console.log('✅ addLiquidity выполнен');
            
        } catch (error) {
            console.log(`⚠️ Ошибка addLiquidity (ожидаемо): ${error.message}`);
        }
        
        // Пытаемся другие методы
        try {
            console.log('🔨 ВЫЗОВ других методов SDK...');
            
            // Получаем информацию о пуле
            const lbPairInfo = await dlmm.getLbPair();
            console.log('✅ getLbPair выполнен');
            
        } catch (error) {
            console.log(`⚠️ Ошибка других методов: ${error.message}`);
        }
        
    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    }
}

async function main() {
    console.log('🎯 НАЧИНАЕМ ПЕРЕХВАТ...');
    
    await captureInitDiscriminators();
    
    console.log('\n🔄 ЗАВЕРШЕНИЕ ПЕРЕХВАТА...');
    const results = stopCapture();
    
    console.log('\n📊 РЕЗУЛЬТАТЫ ПЕРЕХВАТА:');
    console.log('='.repeat(50));
    console.log(`Всего перехвачено инструкций: ${results.totalCaptured}`);
    
    if (Object.keys(results.initDiscriminators).length > 0) {
        console.log('\n🎯 НАЙДЕННЫЕ INIT DISCRIMINATORS:');
        Object.entries(results.initDiscriminators).forEach(([type, data]) => {
            console.log(`\n${type}:`);
            console.log(`   Hex: ${data.hex}`);
            console.log(`   Array: [${data.array.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
            console.log(`   Accounts: ${data.accounts}`);
        });
    } else {
        console.log('❌ INIT DISCRIMINATORS НЕ НАЙДЕНЫ');
        console.log('💡 Возможные причины:');
        console.log('   - SDK не создает init инструкции в этих методах');
        console.log('   - Нужно вызвать другие методы SDK');
        console.log('   - Init инструкции создаются внутри программы');
    }
    
    console.log('\n📋 ВСЕ ПЕРЕХВАЧЕННЫЕ DISCRIMINATORS:');
    Object.entries(results.discriminators).forEach(([hex, data]) => {
        console.log(`\n${data.type}:`);
        console.log(`   Discriminator: ${hex}`);
        console.log(`   Array: [${data.discriminator.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
        console.log(`   Accounts: ${data.accountsCount}`);
        console.log(`   Data: ${data.dataLength} bytes`);
    });
    
    // Сохраняем результаты
    const filename = `meteora-init-discriminators-${Date.now()}.json`;
    saveResults(filename);
    
    console.log('\n🎉 ПЕРЕХВАТ ЗАВЕРШЕН!');
    
    if (Object.keys(results.initDiscriminators).length > 0) {
        console.log('✅ INIT DISCRIMINATORS НАЙДЕНЫ И СОХРАНЕНЫ!');
    } else {
        console.log('⚠️ INIT DISCRIMINATORS НЕ НАЙДЕНЫ');
        console.log('💡 РЕКОМЕНДАЦИЯ: Используй SystemProgram.createAccount');
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('💥 ФАТАЛЬНАЯ ОШИБКА:', error);
        process.exit(1);
    });
}

module.exports = { captureInitDiscriminators };
