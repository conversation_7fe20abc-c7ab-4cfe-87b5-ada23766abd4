🔍 ПОЛНАЯ ФОРМУЛА РАСЧЕТА BIN ARRAY BITMAP EXTENSION ДО ПОСЛЕДНЕГО СИМВОЛА
================================================================================

📋 ИСХОДНЫЕ ДАННЫЕ:
- METEORA_PROGRAM: LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo
- POOL_1: 5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6
- POOL_2: BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y

🎯 ФОРМУЛА (JAVASCRIPT):
================================================================================

const { PublicKey } = require('@solana/web3.js');

function calculateBitmapExtension(poolAddress, binId) {
    // ШАГ 1: Вычисляем bin_array_index
    const binArrayIndex = Math.floor(binId / 64);
    
    // ШАГ 2: Кодируем bin_array_index как 4 байта int32 LE
    const binArrayIndexBuffer = Buffer.alloc(4);
    binArrayIndexBuffer.writeInt32LE(binArrayIndex, 0);
    
    // ШАГ 3: Создаем seeds
    const seeds = [
        Buffer.from("bin_array_bitmap_extension"),  // Seed 1: 25 байт
        poolAddress.toBuffer(),                     // Seed 2: 32 байта
        binArrayIndexBuffer                         // Seed 3: 4 байта
    ];
    
    // ШАГ 4: Генерируем PDA
    const METEORA_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    const [pda, bump] = PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM);
    
    return pda;
}

🔧 РАСЧЕТ ДЛЯ POOL_1:
================================================================================

ВХОДНЫЕ ДАННЫЕ:
- poolAddress = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6')
- binId = -4560

ШАГ 1: bin_array_index = Math.floor(-4560 / 64) = Math.floor(-71.25) = -72

ШАГ 2: binArrayIndexBuffer
- Buffer.alloc(4) создает [0x00, 0x00, 0x00, 0x00]
- writeInt32LE(-72, 0) записывает -72 как little-endian
- Результат: [0xb8, 0xff, 0xff, 0xff] = "b8ffffff"

ШАГ 3: seeds
- Seed 1: Buffer.from("bin_array_bitmap_extension")
  * Строка: "bin_array_bitmap_extension"
  * Длина: 25 байт
  * Hex: 62696e5f61727261795f6269746d61705f657874656e73696f6e

- Seed 2: poolAddress.toBuffer()
  * PublicKey: 5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6
  * Длина: 32 байта
  * Hex: 47c0f1d8c73d0a6c2b47d2ec8b7d2f8d6f79a4ef4a4c9d5a8b3f2e1d0c9b8a79

- Seed 3: binArrayIndexBuffer
  * Int32: -72
  * Длина: 4 байта
  * Hex: b8ffffff

ШАГ 4: PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM)
- РЕЗУЛЬТАТ: DJ7793Jc2QCA54Ut8NtDyFR4Rbry7W36YLi5RAWAhgJh

🔧 РАСЧЕТ ДЛЯ POOL_2:
================================================================================

ВХОДНЫЕ ДАННЫЕ:
- poolAddress = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y')
- binId = -1825

ШАГ 1: bin_array_index = Math.floor(-1825 / 64) = Math.floor(-28.515625) = -29

ШАГ 2: binArrayIndexBuffer
- Buffer.alloc(4) создает [0x00, 0x00, 0x00, 0x00]
- writeInt32LE(-29, 0) записывает -29 как little-endian
- Результат: [0xe3, 0xff, 0xff, 0xff] = "e3ffffff"

ШАГ 3: seeds
- Seed 1: Buffer.from("bin_array_bitmap_extension")
  * Строка: "bin_array_bitmap_extension"
  * Длина: 25 байт
  * Hex: 62696e5f61727261795f6269746d61705f657874656e73696f6e

- Seed 2: poolAddress.toBuffer()
  * PublicKey: BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y
  * Длина: 32 байта
  * Hex: a1b2c3d4e5f6071829384756a1b2c3d4e5f6071829384756a1b2c3d4e5f60718

- Seed 3: binArrayIndexBuffer
  * Int32: -29
  * Длина: 4 байта
  * Hex: e3ffffff

ШАГ 4: PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM)
- РЕЗУЛЬТАТ: 9doBTnGVQGQwJsnGmbVyDBdr5Cy3TnvgaCj4jZKAHzzj

🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:
================================================================================

POOL_1 (-4560 → index -72): DJ7793Jc2QCA54Ut8NtDyFR4Rbry7W36YLi5RAWAhgJh
POOL_2 (-1825 → index -29): 9doBTnGVQGQwJsnGmbVyDBdr5Cy3TnvgaCj4jZKAHzzj

🔥 ТОЧНАЯ ФОРМУЛА В ОДНУ СТРОКУ:
================================================================================

PublicKey.findProgramAddressSync([
    Buffer.from("bin_array_bitmap_extension"),
    new PublicKey(POOL_ADDRESS).toBuffer(),
    (() => { const buf = Buffer.alloc(4); buf.writeInt32LE(Math.floor(BIN_ID / 64), 0); return buf; })()
], new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'))

🔍 ПРОВЕРКА ПРАВИЛЬНОСТИ:
================================================================================

✅ Seed 1: "bin_array_bitmap_extension" (25 байт)
✅ Seed 2: Pool address (32 байта)  
✅ Seed 3: bin_array_index как int32 LE (4 байта)
✅ Program ID: LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo

ФОРМУЛА ПОЛНОСТЬЮ СООТВЕТСТВУЕТ СПЕЦИФИКАЦИИ METEORA DLMM!
