/**
 * 🔥 ПОИСК ПРАВИЛЬНОГО EVENT AUTHORITY ДЛЯ METEORA
 * ГЕНЕРИРУЕМ PDA ДЛЯ EVENT AUTHORITY
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function findEventAuthority() {
    console.log('🔥 ПОИСК ПРАВИЛЬНОГО EVENT AUTHORITY ДЛЯ METEORA...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // Meteora Program ID
        const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

        console.log('📊 ГЕНЕРИРУЕМ EVENT AUTHORITY PDA...');

        // Возможные seeds для Event Authority
        const possibleSeeds = [
            ['__event_authority'],
            ['event_authority'],
            ['EventAuthority'],
            ['event-authority'],
            ['authority'],
            ['events'],
            ['event'],
            ['__events'],
            ['__authority']
        ];

        let foundAuthority = null;

        for (const seeds of possibleSeeds) {
            try {
                console.log(`🔍 Пробуем seeds: [${seeds.map(s => `"${s}"`).join(', ')}]`);
                
                const seedBuffers = seeds.map(seed => Buffer.from(seed, 'utf8'));
                
                const [eventAuthority, bump] = PublicKey.findProgramAddressSync(
                    seedBuffers,
                    METEORA_PROGRAM_ID
                );

                console.log(`   PDA: ${eventAuthority.toString()}`);
                console.log(`   Bump: ${bump}`);

                // Проверяем существование
                const accountInfo = await connection.getAccountInfo(eventAuthority);
                
                if (accountInfo) {
                    console.log(`   ✅ СУЩЕСТВУЕТ! Размер: ${accountInfo.data.length} байт`);
                    console.log(`   💰 Lamports: ${accountInfo.lamports}`);
                    console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
                    
                    if (accountInfo.owner.equals(METEORA_PROGRAM_ID)) {
                        console.log(`   🎯 ПРИНАДЛЕЖИТ METEORA ПРОГРАММЕ!`);
                        foundAuthority = {
                            address: eventAuthority.toString(),
                            seeds: seeds,
                            bump: bump,
                            size: accountInfo.data.length
                        };
                        break;
                    }
                } else {
                    console.log(`   ❌ НЕ СУЩЕСТВУЕТ`);
                }
                
                console.log('');
                
            } catch (error) {
                console.log(`   💥 Ошибка: ${error.message}\n`);
            }
        }

        // Если не найден, пробуем с LB Pair
        if (!foundAuthority) {
            console.log('🔍 ПРОБУЕМ С LB PAIR SEEDS...');
            
            const pool1 = rpcConfig.getMeteoraPool1();
            const lbPairKey = new PublicKey(pool1.lbPair);
            
            const lbPairSeeds = [
                ['__event_authority', lbPairKey.toBuffer()],
                ['event_authority', lbPairKey.toBuffer()],
                ['events', lbPairKey.toBuffer()]
            ];
            
            for (const seeds of lbPairSeeds) {
                try {
                    console.log(`🔍 Пробуем с LB Pair: [${seeds[0]}, lbPair]`);
                    
                    const [eventAuthority, bump] = PublicKey.findProgramAddressSync(
                        seeds,
                        METEORA_PROGRAM_ID
                    );

                    console.log(`   PDA: ${eventAuthority.toString()}`);
                    
                    const accountInfo = await connection.getAccountInfo(eventAuthority);
                    
                    if (accountInfo) {
                        console.log(`   ✅ НАЙДЕН EVENT AUTHORITY С LB PAIR!`);
                        foundAuthority = {
                            address: eventAuthority.toString(),
                            seeds: [seeds[0], 'lbPair'],
                            bump: bump,
                            size: accountInfo.data.length
                        };
                        break;
                    } else {
                        console.log(`   ❌ НЕ СУЩЕСТВУЕТ`);
                    }
                    
                } catch (error) {
                    console.log(`   💥 Ошибка: ${error.message}`);
                }
            }
        }

        // РЕЗУЛЬТАТ
        if (foundAuthority) {
            console.log('\n🎉 НАЙДЕН ПРАВИЛЬНЫЙ EVENT AUTHORITY!');
            console.log(`🔥 Адрес: ${foundAuthority.address}`);
            console.log(`🔥 Seeds: [${foundAuthority.seeds.join(', ')}]`);
            console.log(`🔥 Bump: ${foundAuthority.bump}`);
            console.log(`📊 Размер: ${foundAuthority.size} байт`);
            
            return foundAuthority;
        } else {
            console.log('\n❌ EVENT AUTHORITY НЕ НАЙДЕН!');
            console.log('🔧 ВОЗМОЖНЫЕ РЕШЕНИЯ:');
            console.log('   1. Event Authority может не требоваться для add_liquidity2');
            console.log('   2. Использовать SystemProgram.programId');
            console.log('   3. Найти в исходном коде Meteora SDK');
            
            return null;
        }

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск поиска
if (require.main === module) {
    findEventAuthority()
        .then(result => {
            if (result) {
                console.log('\n✅ EVENT AUTHORITY НАЙДЕН!');
                process.exit(0);
            } else {
                console.log('\n❌ EVENT AUTHORITY НЕ НАЙДЕН!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { findEventAuthority };
