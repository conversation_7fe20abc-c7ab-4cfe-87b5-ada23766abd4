const { Connection, PublicKey, Keypair, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { getAssociatedTokenAddress } = require('@solana/spl-token');
const { BN } = require('bn.js');
const DLMM = require('@meteora-ag/dlmm');
const fs = require('fs');

// 🔥 ИМПОРТ METEORA TRANSACTION BUILDER!
const MeteoraTransactionBuilder = require('./meteora-production-ready.js');

// 🔧 КОНФИГУРАЦИЯ
const RPC_URL = 'https://api.mainnet-beta.solana.com';
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

// 🎯 ПУЛЫ С ПРАВИЛЬНЫМИ RESERVE PDA ИЗ БЕКАПА!
const POOLS = {
    POOL_1: {
        address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
        name: 'WSOL-USDC Pool 1',
        reserveX: 'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // 🔥 ИЗ БЕКАПА!
        reserveY: 'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz'  // 🔥 ИЗ БЕКАПА!
    },
    POOL_2: {
        address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
        name: 'WSOL-USDC Pool 2',
        reserveX: 'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // 🔥 ИЗ БЕКАПА!
        reserveY: '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb'  // 🔥 ИЗ БЕКАПА!
    }
};

// 🔑 ЗАГРУЗКА WALLET ИЗ ФАЙЛА
function loadWallet() {
    const walletPath = process.env.WALLET_PATH || './wallet.json';
    if (fs.existsSync(walletPath)) {
        const walletData = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
        return Keypair.fromSecretKey(new Uint8Array(walletData));
    } else {
        throw new Error('Файл кошелька не найден');
    }
}

/**
 * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ RESERVE PDA
 */
async function checkReservePDA(connection, reserveAddress, poolName) {
    try {
        const accountInfo = await connection.getAccountInfo(new PublicKey(reserveAddress));
        if (accountInfo) {
            console.log(`   ✅ ${poolName} Reserve существует`);
            return true;
        } else {
            console.log(`   ❌ ${poolName} Reserve НЕ существует`);
            return false;
        }
    } catch (error) {
        console.log(`   ❌ ${poolName} Reserve ошибка проверки: ${error.message}`);
        return false;
    }
}

/**
 * 🔥 СОЗДАНИЕ МИНИМАЛЬНОЙ ЛИКВИДНОСТИ ДЛЯ СОЗДАНИЯ RESERVE PDA
 */
async function createMinimalLiquidity(poolAddress, poolName) {
    console.log(`\n🔥 СОЗДАНИЕ МИНИМАЛЬНОЙ ЛИКВИДНОСТИ ДЛЯ ${poolName}:`);
    console.log(`   Pool: ${poolAddress}`);
    
    try {
        const connection = new Connection(RPC_URL, 'confirmed');
        const wallet = loadWallet();
        
        console.log(`   💰 Wallet: ${wallet.publicKey.toString().slice(0,8)}...`);
        
        // 🔥 СОЗДАЕМ DLMM ОБЪЕКТ
        const dlmm = await DLMM.create(connection, new PublicKey(poolAddress));
        console.log(`   ✅ DLMM объект создан`);
        console.log(`   📊 Active Bin: ${dlmm.lbPair.activeId}`);
        console.log(`   🪙 Token X: ${dlmm.lbPair.tokenXMint.toString().slice(0,8)}...`);
        console.log(`   🪙 Token Y: ${dlmm.lbPair.tokenYMint.toString().slice(0,8)}...`);
        
        // 🔥 ПОЛУЧАЕМ USER TOKEN ACCOUNTS
        const userTokenX = await getAssociatedTokenAddress(dlmm.lbPair.tokenXMint, wallet.publicKey);
        const userTokenY = await getAssociatedTokenAddress(dlmm.lbPair.tokenYMint, wallet.publicKey);
        
        console.log(`   🏦 User Token X: ${userTokenX.toString().slice(0,8)}...`);
        console.log(`   🏦 User Token Y: ${userTokenY.toString().slice(0,8)}...`);
        
        // 🔥 СОЗДАЕМ ВРЕМЕННУЮ ПОЗИЦИЮ
        const positionKeypair = Keypair.generate();
        console.log(`   🎯 Временная позиция: ${positionKeypair.publicKey.toString().slice(0,8)}...`);
        
        // 🔥 ПОЛУЧАЕМ АКТИВНЫЙ BIN ID
        const activeBinId = dlmm.lbPair.activeId;
        const minBinId = activeBinId - 1;
        const maxBinId = activeBinId + 1;
        
        console.log(`   📊 Bin IDs: ${minBinId} | ${activeBinId} | ${maxBinId}`);
        
        // 🔥 МИНИМАЛЬНЫЕ СУММЫ ДЛЯ СОЗДАНИЯ RESERVE PDA
        const minAmount = new BN(1000000); // 0.001 SOL или 1 USDC
        
        console.log(`   💰 Минимальная сумма: ${minAmount.toString()} lamports`);
        
        // 🔥 СОЗДАЕМ ADD LIQUIDITY ТРАНЗАКЦИЮ ЧЕРЕЗ MeteoraTransactionBuilder!
        console.log(`   🔧 Создание ADD LIQUIDITY через MeteoraTransactionBuilder...`);
        
        const transactionData = await MeteoraTransactionBuilder.createAddLiquidity2Transaction({
            lbPair: dlmm.pubkey.toString(),
            user: wallet.publicKey.toString(),
            tokenX: dlmm.lbPair.tokenXMint.toString(),
            tokenY: dlmm.lbPair.tokenYMint.toString(),
            userTokenX: userTokenX.toString(),
            userTokenY: userTokenY.toString(),
            amountX: parseInt(minAmount.toString()),
            amountY: parseInt(minAmount.toString()),
            binLiquidityDist: [
                { binId: minBinId, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 },
                { binId: activeBinId, xAmountBpsOfTotal: 3334, yAmountBpsOfTotal: 3334 },
                { binId: maxBinId, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 }
            ],
            positionAddress: positionKeypair.publicKey.toBase58()
        });
        
        console.log(`   ✅ ADD LIQUIDITY транзакция создана!`);
        console.log(`   📊 Аккаунтов: ${transactionData.accounts.length}`);
        console.log(`   📝 Data: ${transactionData.instructionData.length} bytes`);
        
        // 🔥 ПОКАЗЫВАЕМ КАКИЕ RESERVE PDA БУДУТ СОЗДАНЫ!
        console.log(`   🎯 RESERVE PDA КОТОРЫЕ БУДУТ СОЗДАНЫ:`);
        console.log(`     Reserve X: ${transactionData.pdas.reserveX.toBase58()}`);
        console.log(`     Reserve Y: ${transactionData.pdas.reserveY.toBase58()}`);
        
        // 🔥 СОЗДАЕМ И ОТПРАВЛЯЕМ ТРАНЗАКЦИЮ
        const transaction = new Transaction();
        transaction.add(transactionData.instruction);
        
        // 🔥 ДОБАВЛЯЕМ POSITION KEYPAIR КАК SIGNER
        transaction.partialSign(positionKeypair);
        
        console.log(`   🚀 Отправка транзакции...`);
        
        const signature = await sendAndConfirmTransaction(
            connection,
            transaction,
            [wallet, positionKeypair],
            {
                commitment: 'confirmed',
                maxRetries: 3
            }
        );
        
        console.log(`   ✅ ТРАНЗАКЦИЯ ВЫПОЛНЕНА!`);
        console.log(`   🔗 Signature: ${signature}`);
        console.log(`   🌐 Explorer: https://solscan.io/tx/${signature}`);
        
        return true;
        
    } catch (error) {
        console.log(`   ❌ Ошибка создания ликвидности для ${poolName}: ${error.message}`);
        if (error.logs) {
            console.log(`   📋 Логи:`, error.logs);
        }
        return false;
    }
}

/**
 * 🔍 ПРОВЕРКА ВСЕХ RESERVE PDA
 */
async function checkAllReservePDAs() {
    console.log(`\n🔍 ПРОВЕРКА ВСЕХ RESERVE PDA`);
    console.log(`==================================================`);
    
    const connection = new Connection(RPC_URL, 'confirmed');
    
    for (const [poolKey, pool] of Object.entries(POOLS)) {
        console.log(`\n📊 ${poolKey} (${pool.address}):`);
        
        const reserveXExists = await checkReservePDA(connection, pool.reserveX, 'Reserve X');
        const reserveYExists = await checkReservePDA(connection, pool.reserveY, 'Reserve Y');
        
        if (!reserveXExists || !reserveYExists) {
            console.log(`   🔧 НУЖНО СОЗДАТЬ RESERVE PDA ДЛЯ ${pool.name}`);
        } else {
            console.log(`   ✅ ВСЕ RESERVE PDA СУЩЕСТВУЮТ ДЛЯ ${pool.name}`);
        }
    }
}

/**
 * 🔥 СОЗДАНИЕ ВСЕХ RESERVE PDA
 */
async function createAllReservePDAs() {
    console.log(`\n🔥 СОЗДАНИЕ ВСЕХ RESERVE PDA ЧЕРЕЗ ДОБАВЛЕНИЕ ЛИКВИДНОСТИ`);
    console.log(`==================================================`);
    
    for (const [poolKey, pool] of Object.entries(POOLS)) {
        console.log(`\n🎯 ОБРАБОТКА ${poolKey}:`);
        
        const success = await createMinimalLiquidity(pool.address, pool.name);
        
        if (success) {
            console.log(`   ✅ ${pool.name} - RESERVE PDA СОЗДАНЫ!`);
        } else {
            console.log(`   ❌ ${pool.name} - ОШИБКА СОЗДАНИЯ RESERVE PDA!`);
        }
        
        // Пауза между пулами
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log(`\n🎉 СОЗДАНИЕ RESERVE PDA ЗАВЕРШЕНО!`);
}

// 🚀 ОСНОВНАЯ ФУНКЦИЯ
async function main() {
    const command = process.argv[2];
    
    if (command === 'check') {
        await checkAllReservePDAs();
    } else {
        await createAllReservePDAs();
        
        // Проверяем результат
        console.log(`\n🔍 ФИНАЛЬНАЯ ПРОВЕРКА:`);
        await checkAllReservePDAs();
    }
}

// Запуск
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    checkAllReservePDAs,
    createAllReservePDAs,
    createMinimalLiquidity
};
