{"description": "🔥 METEORA DLMM ИНСТРУКЦИИ ДЛЯ РАБОТЫ ТОЛЬКО С АКТИВНЫМ БИНОМ", "version": "1.0.0", "active_bin_only": true, "add_liquidity2_pool1": {"description": "Добавление ликвидности в Pool 1 только в активный бин", "liquidity_parameter": {"amount_x": "<AMOUNT_IN_X>", "amount_y": "<AMOUNT_IN_Y>", "bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 10000, "distribution_y": 10000, "comment": "100% в X токен (WSOL), 100% в Y токен (USDC) - ДВУХСТОРОННЯЯ ЛИКВИДНОСТЬ"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "add_liquidity2_pool2": {"description": "Добавление ликвидности в Pool 2 только в активный бин", "liquidity_parameter": {"amount_x": "<AMOUNT_IN_X>", "amount_y": "<AMOUNT_IN_Y>", "bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 10000, "distribution_y": 10000, "comment": "100% в X токен (WSOL), 100% в Y токен (USDC) - ДВУХСТОРОННЯЯ ЛИКВИДНОСТЬ"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "swap_buy_sol": {"description": "Покупка SOL через активный бин", "amount_in": "<AMOUNT_IN>", "min_amount_out": "0", "bin_array_bitmap_extension": "<bitmapExtensionPDA>", "comment": "Swap автоматически использует активный бин"}, "swap_sell_sol": {"description": "Продажа SOL через активный бин", "amount_in": "<AMOUNT_IN>", "min_amount_out": "0", "bin_array_bitmap_extension": "<bitmapExtensionPDA>", "comment": "Swap автоматически использует активный бин"}, "remove_liquidity_pool1": {"description": "Удаление ликвидности из Pool 1 только из активного бина", "liquidity_parameter": {"amount_x": "0", "amount_y": "0", "bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 0, "distribution_y": 0, "comment": "Удаляем всю ликвидность из активного бина"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "remove_liquidity_pool2": {"description": "Удаление ликвидности из Pool 2 только из активного бина", "liquidity_parameter": {"amount_x": "0", "amount_y": "0", "bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 0, "distribution_y": 0, "comment": "Удаляем всю ликвидность из активного бина"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "claim_fee2_pool1": {"description": "Сбор комиссий из Pool 1 только из активного бина", "liquidity_parameter": {"bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 0, "distribution_y": 0, "comment": "Собираем комиссии только из активного бина"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "claim_fee2_pool2": {"description": "Сбор комиссий из Pool 2 только из активного бина", "liquidity_parameter": {"bin_liquidity_dist": [{"bin_id": "<activeBinId>", "distribution_x": 0, "distribution_y": 0, "comment": "Собираем комиссии только из активного бина"}]}, "remaining_accounts_info": {"slices": [{"accounts": ["<binLiquidityPDA>", "<binArrayPDA>", "<reserveX>", "<reserveY>"], "comment": "Только аккаунты для активного бина"}]}}, "pda_formulas": {"binLiquidityPDA": {"seeds": ["bin", "lbPair", "binId(i32 LE)"], "program": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"}, "binArrayPDA": {"seeds": ["bin_array", "lbPair", "chunkIndex(i64 LE)"], "program": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "note": "chunkIndex = Math.floor(binId / 64)"}, "bitmapExtensionPDA": "УДАЛЕНО - используем заглушку Meteora DLMM Program"}, "optimization_benefits": {"transaction_size_reduction": "~200 bytes экономии", "accounts_reduction": "С 12 до 4 аккаунтов в remaining_accounts", "bin_liquidity_dist_reduction": "С 3 до 1 элемента", "cache_simplification": "Только активный бин в кэше", "performance_improvement": "Быстрее генерация PDA и меньше RPC запросов"}}