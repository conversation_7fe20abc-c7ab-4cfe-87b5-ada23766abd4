/**
 * 🔥 ПРОВЕРКА LB PAIR ИЗ КЭША
 * ПРОВЕРЯЕМ СУЩЕСТВУЕТ ЛИ ПРАВИЛЬНЫЙ LB PAIR
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const rpcConfig = require('./rpc-config.js');

async function checkCachedLBPair() {
    console.log('🔥 ПРОВЕРКА LB PAIR ИЗ КЭША...\n');

    try {
        // Получаем подключение
        const dataRPCs = rpcConfig.getDataRPCs();
        const connection = new Connection(dataRPCs[0].url, 'confirmed');
        console.log(`🔍 Используем RPC: ${dataRPCs[0].name}\n`);

        // LB Pair из кэша (который работал раньше)
        const CACHED_LB_PAIR = '5rCf1DM8K7CDTpGG6b9JTVS8n7eD4BD2Pg4YBNGZzvgF';
        
        console.log(`📊 ПРОВЕРЯЕМ КЭШИРОВАННЫЙ LB PAIR: ${CACHED_LB_PAIR}`);

        const publicKey = new PublicKey(CACHED_LB_PAIR);
        const accountInfo = await connection.getAccountInfo(publicKey);

        if (accountInfo) {
            console.log(`✅ LB PAIR СУЩЕСТВУЕТ!`);
            console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
            console.log(`   💰 Lamports: ${accountInfo.lamports}`);
            console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
            
            // Проверяем что это Meteora программа
            const METEORA_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
            if (accountInfo.owner.toString() === METEORA_PROGRAM_ID) {
                console.log(`   ✅ ПРИНАДЛЕЖИТ METEORA ПРОГРАММЕ!`);
            } else {
                console.log(`   ❌ НЕ ПРИНАДЛЕЖИТ METEORA ПРОГРАММЕ!`);
            }

            // Проверяем данные аккаунта
            const data = accountInfo.data;
            console.log(`\n🔍 АНАЛИЗ ДАННЫХ LB PAIR:`);
            console.log(`   Первые 32 байта: ${data.slice(0, 32).toString('hex')}`);
            
            // Ищем token mints в данных
            const WSOL_MINT = 'So11111111111111111111111111111111111111112';
            const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
            
            const dataHex = data.toString('hex');
            const wsolBuffer = new PublicKey(WSOL_MINT).toBuffer();
            const usdcBuffer = new PublicKey(USDC_MINT).toBuffer();
            
            const wsolHex = wsolBuffer.toString('hex');
            const usdcHex = usdcBuffer.toString('hex');
            
            console.log(`\n🔍 ПОИСК TOKEN MINTS:`);
            console.log(`   WSOL найден: ${dataHex.includes(wsolHex) ? '✅' : '❌'}`);
            console.log(`   USDC найден: ${dataHex.includes(usdcHex) ? '✅' : '❌'}`);
            
            if (dataHex.includes(wsolHex) && dataHex.includes(usdcHex)) {
                console.log(`\n🎯 ЭТО ПРАВИЛЬНЫЙ WSOL-USDC LB PAIR!`);
                
                // Теперь проверим Reserve PDA для этого пула
                console.log(`\n🔍 ПРОВЕРЯЕМ RESERVE PDA ДЛЯ ЭТОГО ПУЛА:`);
                
                // Генерируем Reserve PDA
                const [reserveX] = PublicKey.findProgramAddressSync(
                    [
                        Buffer.from('reserve'),
                        publicKey.toBuffer(),
                        new PublicKey(WSOL_MINT).toBuffer()
                    ],
                    new PublicKey(METEORA_PROGRAM_ID)
                );
                
                const [reserveY] = PublicKey.findProgramAddressSync(
                    [
                        Buffer.from('reserve'),
                        publicKey.toBuffer(),
                        new PublicKey(USDC_MINT).toBuffer()
                    ],
                    new PublicKey(METEORA_PROGRAM_ID)
                );
                
                console.log(`   Reserve X PDA: ${reserveX.toString()}`);
                console.log(`   Reserve Y PDA: ${reserveY.toString()}`);
                
                // Проверяем существование Reserve PDA
                const reserveXInfo = await connection.getAccountInfo(reserveX);
                const reserveYInfo = await connection.getAccountInfo(reserveY);
                
                console.log(`   Reserve X существует: ${reserveXInfo ? '✅' : '❌'}`);
                console.log(`   Reserve Y существует: ${reserveYInfo ? '✅' : '❌'}`);
                
                if (reserveXInfo && reserveYInfo) {
                    console.log(`\n✅ ВСЕ RESERVE PDA СУЩЕСТВУЮТ!`);
                    console.log(`🔥 ИСПОЛЬЗУЙ ЭТОТ LB PAIR: ${CACHED_LB_PAIR}`);
                    return {
                        lbPair: CACHED_LB_PAIR,
                        reserveX: reserveX.toString(),
                        reserveY: reserveY.toString(),
                        valid: true
                    };
                } else {
                    console.log(`\n❌ RESERVE PDA НЕ СУЩЕСТВУЮТ!`);
                }
            } else {
                console.log(`\n❌ НЕ СОДЕРЖИТ WSOL-USDC MINTS!`);
            }
            
        } else {
            console.log(`❌ LB PAIR НЕ СУЩЕСТВУЕТ!`);
        }

        return null;

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        return null;
    }
}

// Запуск проверки
if (require.main === module) {
    checkCachedLBPair()
        .then(result => {
            if (result && result.valid) {
                console.log('\n✅ НАЙДЕН ПРАВИЛЬНЫЙ LB PAIR!');
                console.log(`🔥 LB PAIR: ${result.lbPair}`);
                console.log(`🔥 RESERVE X: ${result.reserveX}`);
                console.log(`🔥 RESERVE Y: ${result.reserveY}`);
                process.exit(0);
            } else {
                console.log('\n❌ ПРАВИЛЬНЫЙ LB PAIR НЕ НАЙДЕН!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { checkCachedLBPair };
