/**
 * 🔍 ДЕТАЛЬНАЯ ОТЛАДКА ОШИБКИ 3007
 * Сравниваем наши аккаунты с рабочим примером
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MeteoraTransactionBuilder } = require('./meteora-production-ready');

async function debugError3007() {
    console.log('🔍 ДЕТАЛЬНАЯ ОТЛАДКА ОШИБКИ 3007');
    console.log('='.repeat(60));

    // Создаем нашу транзакцию
    const ourTransaction = await MeteoraTransactionBuilder.createAddLiquidity2Transaction({
        lbPair: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
        user: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
        tokenX: 'So11111111111111111111111111111111111111112',
        tokenY: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        userTokenX: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
        userTokenY: '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo',
        amountX: 1000000,
        amountY: 1000000,
        binLiquidityDist: [
            { binId: -4518, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 },
            { binId: -4517, xAmountBpsOfTotal: 3334, yAmountBpsOfTotal: 3334 },
            { binId: -4516, xAmountBpsOfTotal: 3333, yAmountBpsOfTotal: 3333 }
        ],
        positionIndex: 0
    });

    // Рабочий пример из твоего сообщения
    const workingExample = [
        { name: 'Position', address: '3hfNeRQBY3xZ3LMgKPbZ1p9bxehTDVppc4w86duRM6JP', writable: true, signer: false },
        { name: 'Lb Pair', address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', writable: true, signer: false },
        { name: 'Bitmap Extension', address: '3VRfGiDSAeppXKgkkq36hsSuSubPexk1aoiKMMSoHg8C', writable: true, signer: false },
        { name: 'User Token X', address: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk', writable: true, signer: false },
        { name: 'User Token Y', address: '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo', writable: true, signer: false },
        { name: 'Reserve X', address: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF', writable: true, signer: false },
        { name: 'Reserve Y', address: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD', writable: true, signer: false },
        { name: 'Token X Mint', address: 'So11111111111111111111111111111111111111112', writable: false, signer: false },
        { name: 'Token Y Mint', address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', writable: false, signer: false },
        { name: 'Sender', address: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', writable: true, signer: true },
        { name: 'Token X Program', address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', writable: false, signer: false },
        { name: 'Token Y Program', address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', writable: false, signer: false },
        { name: 'Event Authority', address: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6', writable: false, signer: false },
        { name: 'Program', address: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', writable: false, signer: false },
        { name: 'Bin Array', address: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', writable: true, signer: false }
    ];

    console.log('\n📊 СРАВНЕНИЕ АККАУНТОВ:');
    console.log('='.repeat(50));

    let differences = [];

    // Сравниваем каждый аккаунт
    for (let i = 0; i < Math.max(ourTransaction.accounts.length, workingExample.length); i++) {
        const our = ourTransaction.accounts[i];
        const working = workingExample[i];

        console.log(`\n${i + 1}. ${working ? working.name : 'MISSING'}:`);

        if (!our) {
            console.log(`   ❌ НАШ: ОТСУТСТВУЕТ`);
            differences.push(`Account ${i + 1}: Missing in our transaction`);
            continue;
        }

        if (!working) {
            console.log(`   ❌ РАБОЧИЙ: ОТСУТСТВУЕТ`);
            console.log(`   НАШ: ${our.pubkey.toBase58()}`);
            differences.push(`Account ${i + 1}: Extra in our transaction`);
            continue;
        }

        const ourAddress = our.pubkey.toBase58();
        const workingAddress = working.address;
        const addressMatch = ourAddress === workingAddress;

        const flagsMatch = our.isWritable === working.writable && our.isSigner === working.signer;

        console.log(`   Адрес: ${addressMatch ? '✅' : '❌'}`);
        console.log(`     НАШ:     ${ourAddress}`);
        console.log(`     РАБОЧИЙ: ${workingAddress}`);
        
        console.log(`   Флаги: ${flagsMatch ? '✅' : '❌'}`);
        console.log(`     НАШ:     W:${our.isWritable} S:${our.isSigner}`);
        console.log(`     РАБОЧИЙ: W:${working.writable} S:${working.signer}`);

        if (!addressMatch) {
            differences.push(`Account ${i + 1} (${working.name}): Address mismatch`);
        }
        if (!flagsMatch) {
            differences.push(`Account ${i + 1} (${working.name}): Flags mismatch`);
        }
    }

    console.log('\n🚨 НАЙДЕННЫЕ РАЗЛИЧИЯ:');
    console.log('='.repeat(40));

    if (differences.length === 0) {
        console.log('✅ НЕТ РАЗЛИЧИЙ В АККАУНТАХ!');
        console.log('💡 Ошибка 3007 может быть вызвана другими факторами:');
        console.log('   - Неправильная instruction data');
        console.log('   - Проблемы с PDA seeds');
        console.log('   - Состояние аккаунтов на блокчейне');
    } else {
        console.log(`❌ НАЙДЕНО ${differences.length} РАЗЛИЧИЙ:`);
        differences.forEach((diff, index) => {
            console.log(`   ${index + 1}. ${diff}`);
        });
    }

    // Проверяем instruction data
    console.log('\n📝 ПРОВЕРКА INSTRUCTION DATA:');
    console.log('='.repeat(35));
    console.log(`Длина: ${ourTransaction.instructionData.length} bytes`);
    console.log(`Hex: ${ourTransaction.instructionData.toString('hex')}`);

    // Проверяем discriminator
    const discriminator = Array.from(ourTransaction.instructionData.slice(0, 8));
    console.log(`Discriminator: [${discriminator.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);

    // Дополнительная проверка PDA
    console.log('\n🎯 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА PDA:');
    console.log('='.repeat(40));

    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    
    // Проверяем критические PDA
    const criticalPDAs = [
        { name: 'Position', address: ourTransaction.pdas.position },
        { name: 'Bitmap Extension', address: ourTransaction.pdas.bitmapExtension },
        { name: 'Reserve X', address: ourTransaction.pdas.reserveX },
        { name: 'Reserve Y', address: ourTransaction.pdas.reserveY }
    ];

    for (const pda of criticalPDAs) {
        try {
            const accountInfo = await connection.getAccountInfo(pda.address);
            console.log(`${pda.name}: ${accountInfo ? 'EXISTS' : 'NOT_EXISTS'}`);
            if (accountInfo) {
                console.log(`   Owner: ${accountInfo.owner.toBase58()}`);
                console.log(`   Data: ${accountInfo.data.length} bytes`);
            }
        } catch (error) {
            console.log(`${pda.name}: ERROR - ${error.message}`);
        }
    }

    return {
        differences: differences.length,
        accountsMatch: differences.length === 0,
        ourAccounts: ourTransaction.accounts.length,
        workingAccounts: workingExample.length
    };
}

if (require.main === module) {
    debugError3007()
        .then(result => {
            console.log(`\n📊 ИТОГО: ${result.differences} различий найдено`);
            process.exit(result.accountsMatch ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { debugError3007 };
