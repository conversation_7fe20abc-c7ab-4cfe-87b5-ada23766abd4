/**
 * 🔥 ПОЛНЫЙ СПИСОК АККАУНТОВ ДЛЯ ALT (ADDRESS LOOKUP TABLE)
 * ВСЕ PDA, СТАТИЧЕСКИЕ АДРЕСА И PROGRAM ID ДЛЯ СЖАТИЯ ТРАНЗАКЦИЙ
 */

const { PublicKey } = require('@solana/web3.js');

// 🎯 METEORA DLMM PROGRAM ID
const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

/**
 * 🔥 УДАЛЕНО: generateBitmapExtensionPDA - используем только заглушку!
 */

/**
 * 📋 ДЕТАЛЬНЫЙ АНАЛИЗ ВСЕХ АККАУНТОВ ДЛЯ ALT ТАБЛИЦЫ
 */
const ACCOUNT_ANALYSIS = {

    // 🔧 PROGRAM IDS
    PROGRAMS: {
        METEORA_DLMM: {
            address: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
            name: 'Meteora DLMM Program',
            type: 'PROGRAM',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2', 'remove_liquidity'],
            role: 'Main program for Meteora DLMM operations',
            dynamic: false,
            signer: false
        },
        TOKEN_PROGRAM: {
            address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
            name: 'SPL Token Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['add_liquidity2', 'swap', 'claim_fee2', 'remove_liquidity'],
            role: 'Token operations (transfer, mint, burn)',
            dynamic: false,
            signer: false
        },
        ASSOCIATED_TOKEN_PROGRAM: {
            address: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
            name: 'Associated Token Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['token_account_creation'],
            role: 'Create associated token accounts',
            dynamic: false,
            signer: false
        },
        SYSTEM_PROGRAM: {
            address: '11111111111111111111111111111111',
            name: 'System Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['account_creation', 'lamport_transfers'],
            role: 'Core Solana system operations',
            dynamic: false,
            signer: false
        },
        COMPUTE_BUDGET: {
            address: 'ComputeBudget111111111111111111111111111111',
            name: 'Compute Budget Program',
            type: 'SYSTEM_PROGRAM',
            canCompress: true,
            usedIn: ['compute_budget_instructions'],
            role: 'Set compute unit limits and prices',
            dynamic: false,
            signer: false
        },
        SYSVAR_INSTRUCTIONS: {
            address: 'Sysvar1nstructions1111111111111111111111111',
            name: 'Sysvar Instructions',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['instruction_introspection'],
            role: 'Access to current instruction data',
            dynamic: false,
            signer: false
        },
        SYSVAR_RENT: {
            address: 'SysvarRent111111111111111111111111111111111',
            name: 'Sysvar Rent',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['rent_calculations'],
            role: 'Rent exemption calculations',
            dynamic: false,
            signer: false
        },
        SYSVAR_CLOCK: {
            address: 'SysvarC1ock11111111111111111111111111111111',
            name: 'Sysvar Clock',
            type: 'SYSVAR',
            canCompress: false, // ❌ SYSVARS НЕ СЖИМАЮТСЯ!
            usedIn: ['timestamp_operations'],
            role: 'Current timestamp and slot info',
            dynamic: false,
            signer: false
        }
    },

    // 💰 TOKEN MINTS (ОБЯЗАТЕЛЬНЫЕ)
    MINTS: {
        WSOL: 'So11111111111111111111111111111111111111112',
        USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
    },

    // 🏊 METEORA POOLS
    POOLS: {
        POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // WSOL-USDC
        POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'  // WSOL-USDC
    },

    // 🎯 METEORA POSITIONS (СТАТИЧЕСКИЕ)
    POSITIONS: {
        POOL_1: 'AegDe4QfxF48RxTXowrcH9yfFR7873WkC9WDKwmJjX2a',
        POOL_2: 'BuKyq1rw3rFQX3JRbVqMcQAcyNs4arTBCWLvQGnu6nKJ'
    },

    // 🏦 RESERVES (ИЗ УСПЕШНЫХ ТРАНЗАКЦИЙ)
    RESERVES: {
        POOL_1_RESERVE_X: 'GyeG98KVGvAa7rPUyHMvGmayKePbTX7k6dm8T86VTfYF',
        POOL_1_RESERVE_Y: 'Epq2e1XDoYCbPHXNFuqXfZWaBPFqXDJjrK1d4DVjZpzD',
        POOL_2_RESERVE_X: 'AxvtUZz9KmobqZMuzu8x1NY5iSZmh6JLd5ZNhKSWxYXq',
        POOL_2_RESERVE_Y: 'DN4HwSwBe474vAJJyQvSL9mCmymBvZQDHa8ah8PD6TEi'
    },

    // 🎫 EVENT AUTHORITY (ОБЩИЙ)
    EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',

    // 🗃️ BIN ARRAYS (НАЙДЕННЫЕ АВТОМАТИЧЕСКИ)
    BIN_ARRAYS: {
        BIN_ARRAY_1: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // index -65
        BIN_ARRAY_2: '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4'  // index -64
    },

    // 🔧 BITMAP EXTENSIONS
    BITMAP_EXTENSIONS: {
        POOL_1: 'DArpuuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE', // Найден в блокчейне
        POOL_2: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'  // Заглушка (Program ID)
    },

    // 🔮 ORACLES
    ORACLES: {
        POOL_1: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
        POOL_2: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj'
    },

    // 👤 USER ACCOUNTS (ДИНАМИЧЕСКИЕ - НЕ ДОБАВЛЯЕМ В ALT)
    // Эти аккаунты уникальны для каждого пользователя
    USER_ACCOUNTS: {
        // WSOL ATA: getAssociatedTokenAddress(WSOL_MINT, userPublicKey)
        // USDC ATA: getAssociatedTokenAddress(USDC_MINT, userPublicKey)
        // User PublicKey: wallet.publicKey
    },

    // 🎮 JUPITER (ДЛЯ АРБИТРАЖА)
    JUPITER: {
        PROGRAM_V6: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'
    },

    // 🏪 MARGINFI (ДЛЯ FLASH LOANS)
    MARGINFI: {
        PROGRAM: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
        GROUP: '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8'
    }
};

/**
 * 🔥 СОЗДАНИЕ ПЛОСКОГО МАССИВА ДЛЯ ALT
 */
function createALTAccountsList() {
    const accounts = [];
    
    // Добавляем все статические аккаунты
    Object.values(ALT_ACCOUNTS.PROGRAMS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.MINTS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.POOLS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.POSITIONS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.RESERVES).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.BIN_ARRAYS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.BITMAP_EXTENSIONS).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.ORACLES).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.JUPITER).forEach(addr => accounts.push(addr));
    Object.values(ALT_ACCOUNTS.MARGINFI).forEach(addr => accounts.push(addr));
    
    // Добавляем Event Authority
    accounts.push(ALT_ACCOUNTS.EVENT_AUTHORITY);
    
    // Убираем дубли
    const uniqueAccounts = [...new Set(accounts)];
    
    console.log(`🔥 СОЗДАН СПИСОК ALT АККАУНТОВ:`);
    console.log(`   📊 Всего уникальных аккаунтов: ${uniqueAccounts.length}`);
    console.log(`   📊 Экономия байт на транзакцию: ${uniqueAccounts.length * 32} → ${uniqueAccounts.length} байт`);
    console.log(`   📊 Экономия: ${uniqueAccounts.length * 31} байт на транзакцию`);
    
    return uniqueAccounts;
}

/**
 * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ АККАУНТОВ
 */
async function verifyALTAccounts(connection) {
    console.log('🔍 ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ ALT АККАУНТОВ...');
    
    const accounts = createALTAccountsList();
    let existsCount = 0;
    let totalCount = 0;
    
    for (const address of accounts) {
        try {
            const pubkey = new PublicKey(address);
            const accountInfo = await connection.getAccountInfo(pubkey);
            
            totalCount++;
            if (accountInfo) {
                existsCount++;
                console.log(`   ✅ ${address.slice(0,8)}... - EXISTS`);
            } else {
                console.log(`   ❌ ${address.slice(0,8)}... - NOT EXISTS`);
            }
            
        } catch (error) {
            console.log(`   💥 ${address.slice(0,8)}... - ERROR: ${error.message}`);
            totalCount++;
        }
    }
    
    console.log(`\n🎯 РЕЗУЛЬТАТ ПРОВЕРКИ:`);
    console.log(`   ✅ Существующие: ${existsCount}/${totalCount}`);
    console.log(`   📊 Процент успеха: ${Math.round((existsCount / totalCount) * 100)}%`);
    
    return { existsCount, totalCount, accounts };
}

/**
 * 💾 ЭКСПОРТ ДЛЯ ИСПОЛЬЗОВАНИЯ В ДРУГИХ ФАЙЛАХ
 */
module.exports = {
    ALT_ACCOUNTS,
    createALTAccountsList,
    verifyALTAccounts,
    generateBitmapExtensionPDA,
    METEORA_DLMM_PROGRAM
};

// 🔥 ЕСЛИ ЗАПУСКАЕТСЯ НАПРЯМУЮ - ПОКАЗЫВАЕМ СПИСОК
if (require.main === module) {
    console.log('🔥 METEORA ALT ACCOUNTS GENERATOR');
    console.log('='.repeat(50));
    
    const accounts = createALTAccountsList();
    
    console.log('\n📋 ПОЛНЫЙ СПИСОК АККАУНТОВ:');
    accounts.forEach((addr, index) => {
        console.log(`   ${(index + 1).toString().padStart(3, ' ')}. ${addr}`);
    });
    
    console.log('\n🎯 ГОТОВО! Используйте этот список для создания ALT таблицы.');
}
