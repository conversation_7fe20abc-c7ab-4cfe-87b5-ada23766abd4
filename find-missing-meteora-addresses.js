/**
 * 🔍 ПОИСК ВСЕХ НЕДОСТАЮЩИХ METEORA АДРЕСОВ ДЛЯ ALT ТАБЛИЦЫ
 * АНАЛИЗ ТЕКУЩЕЙ CUSTOM ALT И ПОИСК НОВЫХ PDA
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

// Meteora DLMM Program ID
const METEORA_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

// Наши пулы
const POOLS = {
    POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
};

// Известные статические адреса
const KNOWN_METEORA_ADDRESSES = {
    // Program IDs
    METEORA_DLMM_PROGRAM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    
    // Event Authority
    EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
    
    // Pools
    POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
    
    // Bitmap Extensions
    BITMAP_EXTENSION_POOL_1: 'DArpuuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE',
    
    // Oracles
    ORACLE_POOL_1: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
    ORACLE_POOL_2: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj',
    
    // Bin Arrays (найденные)
    BIN_ARRAY_1: '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4',
    BIN_ARRAY_2: '3PtxzAh6LxUteSy97M9thBkZQHfzWbRLSi8G6oJjfynd',
    
    // Positions (проверенные)
    POSITION_POOL_1: 'AegDe4QfxF48RxTXowrcH9yfFR7873WkC9WDKwmJjX2a',
    POSITION_POOL_2: 'BuKyq1rw3rFQX3JRbVqMcQAcyNs4arTBCWLvQGnu6nKJ'
};

/**
 * 🔧 ГЕНЕРАЦИЯ RESERVE PDA
 */
function generateReservePDA(poolAddress, tokenMint) {
    const seeds = [
        Buffer.from('reserve'),
        new PublicKey(poolAddress).toBuffer(),
        new PublicKey(tokenMint).toBuffer()
    ];
    
    const [pda, bump] = PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM);
    return { pda: pda.toString(), bump };
}

/**
 * 🔧 ГЕНЕРАЦИЯ BIN ARRAY PDA
 */
function generateBinArrayPDA(poolAddress, binArrayIndex) {
    const indexBuffer = Buffer.alloc(8);
    indexBuffer.writeBigInt64LE(BigInt(binArrayIndex), 0);
    
    const seeds = [
        Buffer.from('bin_array'),
        new PublicKey(poolAddress).toBuffer(),
        indexBuffer
    ];
    
    const [pda, bump] = PublicKey.findProgramAddressSync(seeds, METEORA_PROGRAM);
    return { pda: pda.toString(), bump };
}

/**
 * 🔥 УДАЛЕНО: generateBitmapExtensionPDA - используем только заглушку!
 */

/**
 * 📋 ЗАГРУЗКА ТЕКУЩЕЙ CUSTOM ALT ТАБЛИЦЫ
 */
function loadCurrentCustomALT() {
    try {
        const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        return new Set(altData.custom.addresses || []);
    } catch (error) {
        console.log(`⚠️ Ошибка загрузки custom-alt-data.json: ${error.message}`);
        return new Set();
    }
}

/**
 * 🔍 ПОИСК ВСЕХ НЕДОСТАЮЩИХ АДРЕСОВ
 */
async function findMissingMeteoraAddresses() {
    console.log('🔍 ПОИСК ВСЕХ НЕДОСТАЮЩИХ METEORA АДРЕСОВ ДЛЯ ALT ТАБЛИЦЫ');
    console.log('=' .repeat(80));

    // Подключение к RPC
    const connection = new Connection(process.env.SOLANA_RPC_URL);
    console.log('✅ Подключение к RPC установлено');

    // Загружаем текущую ALT таблицу
    const currentALT = loadCurrentCustomALT();
    console.log(`📊 Текущих адресов в custom ALT: ${currentALT.size}`);

    const candidateAddresses = [];
    const existingAddresses = [];
    const nonExistingAddresses = [];

    // 1. Проверяем все известные статические адреса
    console.log('\n🔍 ПРОВЕРЯЕМ ИЗВЕСТНЫЕ СТАТИЧЕСКИЕ АДРЕСА:');
    
    for (const [name, address] of Object.entries(KNOWN_METEORA_ADDRESSES)) {
        if (currentALT.has(address)) {
            console.log(`   ✅ ${name}: УЖЕ В ALT`);
            existingAddresses.push({ name, address, status: 'IN_ALT' });
        } else {
            console.log(`   🆕 ${name}: ОТСУТСТВУЕТ В ALT`);
            candidateAddresses.push({ name, address, type: 'STATIC' });
        }
    }

    // 2. Генерируем и проверяем Reserve PDA
    console.log('\n🔍 ГЕНЕРИРУЕМ RESERVE PDA:');
    
    const tokens = {
        WSOL: 'So11111111111111111111111111111111111111112',
        USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    };

    for (const [poolName, poolAddress] of Object.entries(POOLS)) {
        for (const [tokenName, tokenMint] of Object.entries(tokens)) {
            const reserve = generateReservePDA(poolAddress, tokenMint);
            const name = `${poolName}_RESERVE_${tokenName}`;
            
            if (currentALT.has(reserve.pda)) {
                console.log(`   ✅ ${name}: УЖЕ В ALT`);
                existingAddresses.push({ name, address: reserve.pda, status: 'IN_ALT' });
            } else {
                console.log(`   🆕 ${name}: ${reserve.pda.slice(0,8)}...`);
                candidateAddresses.push({ name, address: reserve.pda, type: 'RESERVE_PDA' });
            }
        }
    }

    // 3. Генерируем Bin Array PDA для разных индексов
    console.log('\n🔍 ГЕНЕРИРУЕМ BIN ARRAY PDA:');
    
    const binArrayIndexes = [-66, -65, -64, -63, -62]; // Диапазон вокруг активных бинов
    
    for (const [poolName, poolAddress] of Object.entries(POOLS)) {
        for (const index of binArrayIndexes) {
            const binArray = generateBinArrayPDA(poolAddress, index);
            const name = `${poolName}_BIN_ARRAY_${index}`;
            
            if (currentALT.has(binArray.pda)) {
                console.log(`   ✅ ${name}: УЖЕ В ALT`);
                existingAddresses.push({ name, address: binArray.pda, status: 'IN_ALT' });
            } else {
                console.log(`   🆕 ${name}: ${binArray.pda.slice(0,8)}...`);
                candidateAddresses.push({ name, address: binArray.pda, type: 'BIN_ARRAY_PDA' });
            }
        }
    }

    // 4. 🔥 УДАЛЕНО: Bitmap Extension PDA - используем только заглушку!
    console.log('\n🔥 BITMAP EXTENSION PDA УДАЛЕН - ИСПОЛЬЗУЕМ ЗАГЛУШКУ!');

    // 5. Проверяем существование кандидатов в блокчейне
    console.log('\n🔍 ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ КАНДИДАТОВ В БЛОКЧЕЙНЕ:');
    
    const validCandidates = [];
    
    for (const candidate of candidateAddresses) {
        try {
            const pubkey = new PublicKey(candidate.address);
            const accountInfo = await connection.getAccountInfo(pubkey);
            
            if (accountInfo) {
                console.log(`   ✅ ${candidate.name}: СУЩЕСТВУЕТ (${accountInfo.data.length} байт)`);
                validCandidates.push({
                    ...candidate,
                    exists: true,
                    size: accountInfo.data.length,
                    owner: accountInfo.owner.toString()
                });
            } else {
                console.log(`   ❌ ${candidate.name}: НЕ СУЩЕСТВУЕТ`);
                nonExistingAddresses.push({ ...candidate, exists: false });
            }
            
        } catch (error) {
            console.log(`   💥 ${candidate.name}: ОШИБКА - ${error.message}`);
            nonExistingAddresses.push({ ...candidate, exists: false, error: error.message });
        }
        
        // Задержка между запросами
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 6. Результаты
    console.log('\n📊 РЕЗУЛЬТАТЫ АНАЛИЗА:');
    console.log(`   📋 Всего проверено адресов: ${Object.keys(KNOWN_METEORA_ADDRESSES).length + candidateAddresses.length}`);
    console.log(`   ✅ Уже в ALT: ${existingAddresses.length}`);
    console.log(`   🆕 Новых существующих: ${validCandidates.length}`);
    console.log(`   ❌ Не существующих: ${nonExistingAddresses.length}`);

    if (validCandidates.length > 0) {
        console.log('\n🎯 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ В ALT:');
        validCandidates.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr.address} // ${addr.name} (${addr.type})`);
        });
        
        console.log(`\n💾 ЭКОНОМИЯ: ${validCandidates.length} × 32 bytes = ${validCandidates.length * 32} bytes`);
    } else {
        console.log('\n✅ ВСЕ НУЖНЫЕ АДРЕСА УЖЕ ЕСТЬ В ALT ТАБЛИЦЕ!');
    }

    // Сохраняем результат
    const result = {
        timestamp: new Date().toISOString(),
        summary: {
            totalChecked: Object.keys(KNOWN_METEORA_ADDRESSES).length + candidateAddresses.length,
            alreadyInALT: existingAddresses.length,
            newValidCandidates: validCandidates.length,
            nonExisting: nonExistingAddresses.length
        },
        validCandidates,
        existingAddresses,
        nonExistingAddresses
    };

    fs.writeFileSync('missing-meteora-addresses.json', JSON.stringify(result, null, 2));
    console.log('\n✅ Результат сохранен в: missing-meteora-addresses.json');

    return result;
}

// Запуск анализа
if (require.main === module) {
    findMissingMeteoraAddresses().catch(console.error);
}

module.exports = { findMissingMeteoraAddresses };
