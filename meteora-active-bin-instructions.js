/**
 * 🔥 METEORA DLMM ИНСТРУКЦИИ ДЛЯ АКТИВНОГО БИНА
 * Все инструкции переписаны под работу только с активным бином
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');

class MeteoraActiveBinInstructions {
    constructor(activeBinCache, rpcConfig) {
        this.activeBinCache = activeBinCache;
        this.rpcConfig = rpcConfig;
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    }

    /**
     * 🔥 ADD_LIQUIDITY2 ДЛЯ АКТИВНОГО БИНА
     */
    createAddLiquidity2(poolAddress, amountX, amountY) {
        const activeBinData = this.activeBinCache.getActiveBin(poolAddress);
        if (!activeBinData) {
            throw new Error(`Нет данных активного бина для ${poolAddress}`);
        }

        const poolConfig = this.getPoolConfig(poolAddress);
        
        // 🔥 СТРУКТУРА ДАННЫХ ДЛЯ АКТИВНОГО БИНА
        const data = Buffer.alloc(200); // Уменьшенный размер
        let offset = 0;

        // 1. Discriminator (8 bytes)
        data.writeUInt32LE(0x12345678, offset); // Placeholder
        offset += 8;

        // 2. Amount X (8 bytes)
        data.writeBigUInt64LE(BigInt(amountX), offset);
        offset += 8;

        // 3. Amount Y (8 bytes) 
        data.writeBigUInt64LE(BigInt(amountY), offset);
        offset += 8;

        // 4. bin_liquidity_dist: Vec length = 1 (ТОЛЬКО АКТИВНЫЙ БИН!)
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 5. Активный бин данные
        data.writeInt32LE(activeBinData.activeBinId, offset); // bin_id
        offset += 4;
        data.writeBigUInt64LE(10000n, offset); // distribution_x = 100%
        offset += 8;
        data.writeBigUInt64LE(10000n, offset); // distribution_y = 100% (ДВУХСТОРОННЯЯ ЛИКВИДНОСТЬ!)
        offset += 8;

        // 6. remaining_accounts_info: Vec length = 1 (ОДИН SLICE!)
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 7. Slice данные (4 аккаунта)
        data.writeUInt32LE(4, offset); // количество аккаунтов
        offset += 4;

        // 8. Записываем 4 pubkey для активного бина
        this.writePubkey(data, offset, new PublicKey(activeBinData.binLiquidityPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(activeBinData.binArrayPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveX));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveY));
        offset += 32;

        console.log(`✅ ADD_LIQUIDITY2 для активного бина ${activeBinData.activeBinId}: ${data.length} bytes`);
        return data.slice(0, offset);
    }

    /**
     * 🔥 REMOVE_LIQUIDITY ДЛЯ АКТИВНОГО БИНА
     */
    createRemoveLiquidity(poolAddress, binLiquidityRemoval) {
        const activeBinData = this.activeBinCache.getActiveBin(poolAddress);
        if (!activeBinData) {
            throw new Error(`Нет данных активного бина для ${poolAddress}`);
        }

        const poolConfig = this.getPoolConfig(poolAddress);
        
        const data = Buffer.alloc(200);
        let offset = 0;

        // 1. Discriminator
        data.writeUInt32LE(0x87654321, offset);
        offset += 8;

        // 2. bin_liquidity_removal: Vec length = 1
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 3. Активный бин removal данные
        data.writeInt32LE(activeBinData.activeBinId, offset); // bin_id
        offset += 4;
        data.writeBigUInt64LE(BigInt(binLiquidityRemoval), offset); // bps_to_remove
        offset += 8;

        // 4. remaining_accounts_info: Vec length = 1
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 5. Slice данные (4 аккаунта)
        data.writeUInt32LE(4, offset);
        offset += 4;

        // 6. Pubkeys для активного бина
        this.writePubkey(data, offset, new PublicKey(activeBinData.binLiquidityPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(activeBinData.binArrayPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveX));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveY));
        offset += 32;

        console.log(`✅ REMOVE_LIQUIDITY для активного бина ${activeBinData.activeBinId}: ${data.length} bytes`);
        return data.slice(0, offset);
    }

    /**
     * 🔥 CLAIM_FEE2 ДЛЯ АКТИВНОГО БИНА
     */
    createClaimFee2(poolAddress) {
        const activeBinData = this.activeBinCache.getActiveBin(poolAddress);
        if (!activeBinData) {
            throw new Error(`Нет данных активного бина для ${poolAddress}`);
        }

        const poolConfig = this.getPoolConfig(poolAddress);
        
        const data = Buffer.alloc(150);
        let offset = 0;

        // 1. Discriminator
        data.writeUInt32LE(0xABCDEF12, offset);
        offset += 8;

        // 2. bin_liquidity_dist: Vec length = 1
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 3. Активный бин данные
        data.writeInt32LE(activeBinData.activeBinId, offset); // bin_id
        offset += 4;
        data.writeBigUInt64LE(10000n, offset); // distribution_x = 10000 (ДВУХСТОРОННЯЯ!)
        offset += 8;
        data.writeBigUInt64LE(10000n, offset); // distribution_y = 10000 (ДВУХСТОРОННЯЯ!)
        offset += 8;

        // 4. remaining_accounts_info: Vec length = 1
        data.writeUInt32LE(1, offset);
        offset += 4;

        // 5. Slice данные (4 аккаунта)
        data.writeUInt32LE(4, offset);
        offset += 4;

        // 6. Pubkeys для активного бина
        this.writePubkey(data, offset, new PublicKey(activeBinData.binLiquidityPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(activeBinData.binArrayPDA));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveX));
        offset += 32;
        this.writePubkey(data, offset, new PublicKey(poolConfig.reserveY));
        offset += 32;

        console.log(`✅ CLAIM_FEE2 для активного бина ${activeBinData.activeBinId}: ${data.length} bytes`);
        return data.slice(0, offset);
    }

    /**
     * 🔥 SWAP (BUY/SELL SOL) - ИСПОЛЬЗУЕТ АКТИВНЫЙ БИН АВТОМАТИЧЕСКИ
     */
    createSwap(poolAddress, amountIn, minAmountOut, swapYtoX = false) {
        const activeBinData = this.activeBinCache.getActiveBin(poolAddress);
        if (!activeBinData) {
            throw new Error(`Нет данных активного бина для ${poolAddress}`);
        }

        // 🔥 ЗАГЛУШКА: ИСПОЛЬЗУЕМ METEORA DLMM PROGRAM
        const bitmapExtensionPDA = this.METEORA_DLMM_PROGRAM;

        const data = Buffer.alloc(50);
        let offset = 0;

        // 1. Discriminator
        data.writeUInt32LE(swapYtoX ? 0x11111111 : 0x22222222, offset);
        offset += 8;

        // 2. Amount In
        data.writeBigUInt64LE(BigInt(amountIn), offset);
        offset += 8;

        // 3. Min Amount Out
        data.writeBigUInt64LE(BigInt(minAmountOut), offset);
        offset += 8;

        console.log(`✅ SWAP для активного бина ${activeBinData.activeBinId}: ${swapYtoX ? 'SELL' : 'BUY'} SOL`);
        console.log(`   🔥 ЗАГЛУШКА: Bitmap Extension = Meteora DLMM Program`);
        
        return {
            data: data.slice(0, offset),
            bitmapExtensionPDA: bitmapExtensionPDA.toString()
        };
    }

    /**
     * 🔧 ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ
     */
    writePubkey(buffer, offset, pubkey) {
        const pubkeyBytes = pubkey.toBuffer();
        pubkeyBytes.copy(buffer, offset);
    }

    getPoolConfig(poolAddress) {
        const poolStr = poolAddress.toString();
        
        if (poolStr === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6') {
            return this.rpcConfig.meteora.pools.POOL_1;
        }
        
        if (poolStr === 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y') {
            return this.rpcConfig.meteora.pools.POOL_2;
        }
        
        throw new Error(`Неизвестный пул: ${poolStr}`);
    }
}

/**
 * 🔥 УПРОЩЕННЫЙ АНАЛИЗАТОР ДЛЯ АКТИВНОГО БИНА
 */
class ActiveBinAnalyzer {
    constructor(activeBinCache) {
        this.activeBinCache = activeBinCache;
    }

    /**
     * 🔥 АНАЛИЗ АРБИТРАЖА МЕЖДУ ДВУМЯ АКТИВНЫМИ БИНАМИ
     */
    analyzeArbitrage(pool1Address, pool2Address) {
        const pool1Data = this.activeBinCache.getActiveBin(pool1Address);
        const pool2Data = this.activeBinCache.getActiveBin(pool2Address);

        if (!pool1Data || !pool2Data) {
            return {
                profitable: false,
                error: 'Нет данных активных бинов'
            };
        }

        const price1 = pool1Data.activeBin.price;
        const price2 = pool2Data.activeBin.price;

        // Простой анализ разности цен
        const priceDiff = Math.abs(price1 - price2);
        const avgPrice = (price1 + price2) / 2;
        const priceDiffPercent = (priceDiff / avgPrice) * 100;

        const profitable = priceDiffPercent > 0.1; // 0.1% минимальная разность

        return {
            profitable,
            pool1: {
                address: pool1Address,
                activeBinId: pool1Data.activeBinId,
                price: price1,
                liquidity: pool1Data.activeBin.liquidityX + pool1Data.activeBin.liquidityY
            },
            pool2: {
                address: pool2Address,
                activeBinId: pool2Data.activeBinId,
                price: price2,
                liquidity: pool2Data.activeBin.liquidityX + pool2Data.activeBin.liquidityY
            },
            priceDiff,
            priceDiffPercent,
            direction: price1 > price2 ? 'POOL1_TO_POOL2' : 'POOL2_TO_POOL1'
        };
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ СТАТИСТИКИ АКТИВНЫХ БИНОВ
     */
    getActiveBinStats() {
        const stats = this.activeBinCache.getStats();

        console.log('📊 СТАТИСТИКА АКТИВНЫХ БИНОВ:');
        console.log(`   Готовность кэша: ${stats.ready ? '✅' : '❌'}`);
        console.log(`   Пулов в кэше: ${stats.cachedPools}/${stats.totalPools}`);

        Object.entries(stats.pools).forEach(([poolShort, data]) => {
            console.log(`   ${poolShort}: бин ${data.activeBinId}, цена ${data.price}, возраст ${data.age}ms`);
        });

        return stats;
    }
}

module.exports = { MeteoraActiveBinInstructions, ActiveBinAnalyzer };
