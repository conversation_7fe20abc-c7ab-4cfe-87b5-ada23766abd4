/**
 * 🔥 ОТЛАДКА КЭШИРОВАНИЯ ДЛЯ НАШЕГО ПУЛА
 * ПРОВЕРЯЕМ ПОЧЕМУ НЕ РАБОТАЕТ ДЛЯ 5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6
 */

const MeteoraBinCacheManager = require('./meteora-bin-cache-manager-clean.js');

async function debugCacheForOurPool() {
    console.log('🔥 ОТЛАДКА КЭШИРОВАНИЯ ДЛЯ НАШЕГО ПУЛА...\n');

    try {
        // Создаем кэш менеджер
        const manager = new MeteoraBinCacheManager();
        
        // НАШ ПУЛ
        const ourPool = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6';
        
        console.log('📊 ИНФОРМАЦИЯ О КЭШЕ:');
        console.log(`   Наш пул: ${ourPool}`);
        console.log(`   Автообновление частота: ${manager.autoUpdateFrequency}ms`);
        console.log('');

        // Ждем 8 секунд чтобы автообновление сработало
        console.log('⏳ ЖДЕМ 8 СЕКУНД ДЛЯ АВТООБНОВЛЕНИЯ...');
        await new Promise(resolve => setTimeout(resolve, 8000));

        // ПРОВЕРЯЕМ КЭШИРОВАННЫЕ ДАННЫЕ
        console.log('\n🔍 ПРОВЕРКА КЭШИРОВАННЫХ ДАННЫХ:');
        
        // Проверяем binArraysCache
        console.log('📋 BIN ARRAYS CACHE:');
        const binArraysKeys = Array.from(manager.binArraysCache.keys());
        console.log(`   Ключи в кэше: ${binArraysKeys.length} шт.`);
        binArraysKeys.forEach(key => {
            const data = manager.binArraysCache.get(key);
            console.log(`   ${key.slice(0, 8)}: активный бин ${data.activeBinId}, бинов: ${data.threeBins?.length || 0}, bin arrays: ${data.binArrays?.length || 0}`);
        });

        // Проверяем dlmmInstancesCache
        console.log('\n📋 DLMM INSTANCES CACHE:');
        const dlmmKeys = Array.from(manager.dlmmInstancesCache.keys());
        console.log(`   Ключи в кэше: ${dlmmKeys.length} шт.`);
        dlmmKeys.forEach(key => {
            console.log(`   ${key.slice(0, 8)}: DLMM instance сохранен`);
        });

        // ПЫТАЕМСЯ ПОЛУЧИТЬ ДАННЫЕ ДЛЯ НАШЕГО ПУЛА
        console.log('\n🔍 ПОЛУЧЕНИЕ ДАННЫХ ДЛЯ НАШЕГО ПУЛА:');
        
        try {
            const arbitrageData = await manager.getArbitrageData(ourPool);
            console.log('✅ ДАННЫЕ ПОЛУЧЕНЫ:');
            console.log(`   Активный бин ID: ${arbitrageData.activeBinId}`);
            console.log(`   Цена: ${arbitrageData.activeBinPrice}`);
            console.log(`   Количество бинов: ${arbitrageData.threeBins?.length || 0}`);
            console.log(`   Количество bin arrays: ${arbitrageData.binArrays?.length || 0}`);
            
            if (arbitrageData.threeBins) {
                console.log('   Бины:');
                arbitrageData.threeBins.forEach((bin, index) => {
                    console.log(`     ${index + 1}. Bin ID: ${bin.binId}, Active: ${bin.isActive}, Price: ${bin.price}`);
                });
            }
            
            if (arbitrageData.binArrays) {
                console.log('   Bin Arrays:');
                arbitrageData.binArrays.forEach((binArray, index) => {
                    console.log(`     ${index + 1}. ${binArray}`);
                });
            }
            
        } catch (error) {
            console.log(`❌ ОШИБКА ПОЛУЧЕНИЯ ДАННЫХ: ${error.message}`);
        }

        // ПРИНУДИТЕЛЬНО ОБНОВЛЯЕМ НАШ ПУЛ
        console.log('\n🔥 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ НАШЕГО ПУЛА:');
        
        try {
            await manager.updateBinArraysForPool(ourPool);
            console.log('✅ ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ ЗАВЕРШЕНО');
            
            // Проверяем данные после обновления
            const updatedData = manager.binArraysCache.get(ourPool);
            if (updatedData) {
                console.log('✅ ДАННЫЕ ПОСЛЕ ОБНОВЛЕНИЯ:');
                console.log(`   Активный бин ID: ${updatedData.activeBinId}`);
                console.log(`   Количество бинов: ${updatedData.threeBins?.length || 0}`);
                console.log(`   Количество bin arrays: ${updatedData.binArrays?.length || 0}`);
                
                if (updatedData.threeBins) {
                    console.log('   Бины после обновления:');
                    updatedData.threeBins.forEach((bin, index) => {
                        console.log(`     ${index + 1}. Bin ID: ${bin.binId}, Active: ${bin.isActive}`);
                    });
                }
                
                if (updatedData.binArrays) {
                    console.log('   Bin Arrays после обновления:');
                    updatedData.binArrays.forEach((binArray, index) => {
                        console.log(`     ${index + 1}. ${binArray}`);
                    });
                }
            } else {
                console.log('❌ НЕТ ДАННЫХ ПОСЛЕ ОБНОВЛЕНИЯ!');
            }
            
        } catch (error) {
            console.log(`❌ ОШИБКА ПРИНУДИТЕЛЬНОГО ОБНОВЛЕНИЯ: ${error.message}`);
        }

        // ПРОВЕРЯЕМ МЕТОД createAddLiquidity2Transaction
        console.log('\n🔍 ПРОВЕРКА ИНТЕГРАЦИИ С createAddLiquidity2Transaction:');
        
        try {
            // Получаем кэшированные данные
            const cachedData = manager.binArraysCache.get(ourPool);
            
            if (cachedData && cachedData.threeBins && cachedData.threeBins.length === 3) {
                console.log('✅ КЭШИРОВАННЫЕ ДАННЫЕ НАЙДЕНЫ');
                
                // Создаем binLiquidityDist из кэшированных данных
                const binLiquidityDist = cachedData.threeBins.map(bin => ({
                    binId: bin.binId,
                    xAmountBpsOfTotal: 3333,
                    yAmountBpsOfTotal: 3333
                }));
                
                console.log('📋 BIN LIQUIDITY DIST ИЗ КЭША:');
                binLiquidityDist.forEach((bin, index) => {
                    console.log(`   ${index + 1}. Bin ID: ${bin.binId}, X BPS: ${bin.xAmountBpsOfTotal}, Y BPS: ${bin.yAmountBpsOfTotal}`);
                });
                
                // Проверяем bin arrays из кэша
                if (cachedData.binArrays && cachedData.binArrays.length > 0) {
                    console.log('✅ BIN ARRAYS ИЗ КЭША:');
                    cachedData.binArrays.forEach((binArray, index) => {
                        console.log(`   ${index + 1}. ${binArray}`);
                    });
                } else {
                    console.log('❌ НЕТ BIN ARRAYS В КЭШЕ!');
                }
                
            } else {
                console.log('❌ НЕТ КЭШИРОВАННЫХ ДАННЫХ ДЛЯ СОЗДАНИЯ ТРАНЗАКЦИИ!');
            }
            
        } catch (error) {
            console.log(`❌ ОШИБКА ПРОВЕРКИ ИНТЕГРАЦИИ: ${error.message}`);
        }

        // ВЫВОДЫ
        console.log('\n🎯 ВЫВОДЫ:');
        console.log('1. Проверили автообновление кэша');
        console.log('2. Проверили принудительное обновление');
        console.log('3. Проверили интеграцию с createAddLiquidity2Transaction');
        console.log('4. Определили где проблема в кэшировании');

        return {
            binArraysCacheSize: manager.binArraysCache.size,
            dlmmCacheSize: manager.dlmmInstancesCache.size,
            ourPoolCached: manager.binArraysCache.has(ourPool),
            cachedData: manager.binArraysCache.get(ourPool)
        };

    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        console.error(error.stack);
        return null;
    }
}

// Запуск отладки
if (require.main === module) {
    debugCacheForOurPool()
        .then(result => {
            if (result) {
                console.log('\n✅ ОТЛАДКА ЗАВЕРШЕНА!');
                console.log(`📊 Результаты: bin arrays cache: ${result.binArraysCacheSize}, dlmm cache: ${result.dlmmCacheSize}, наш пул в кэше: ${result.ourPoolCached}`);
                process.exit(0);
            } else {
                console.log('\n❌ ОТЛАДКА НЕ УДАЛАСЬ!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { debugCacheForOurPool };
